2025-04-27 09:41:33.654 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-27 09:41:33.678 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-27 09:41:33.678 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Intent prediction URL: http://10.68.41.41:8000
2025-04-27 09:41:33.684 [main] INFO  com.unimas.ollamaui.client.IntentPredictionClient - Initialized Intent Prediction client with URL: http://10.68.41.41:8000
2025-04-27 09:41:33.689 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:41:34.091 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"message":"1+1等于几"}
2025-04-27 09:41:34.172 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 422
2025-04-27 09:41:34.174 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"1+1等于几"}}]}
2025-04-27 09:41:34.174 [main] ERROR com.unimas.ollamaui.client.RestClient - Request failed with status code: 422
2025-04-27 09:41:34.176 [main] ERROR com.unimas.ollamaui.client.IntentPredictionClient - Error predicting intent: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"1+1等于几"}}]}
java.io.IOException: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"1+1等于几"}}]}
	at com.unimas.ollamaui.client.RestClient.handleResponse(RestClient.java:189) ~[classes/:?]
	at com.unimas.ollamaui.client.RestClient.post(RestClient.java:91) ~[classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.predictIntent(IntentPredictionClient.java:58) [classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.main(IntentPredictionClient.java:107) [classes/:?]
2025-04-27 09:41:34.183 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:41:34.187 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"message":"查询订单状态"}
2025-04-27 09:41:34.192 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 422
2025-04-27 09:41:34.193 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"查询订单状态"}}]}
2025-04-27 09:41:34.193 [main] ERROR com.unimas.ollamaui.client.RestClient - Request failed with status code: 422
2025-04-27 09:41:34.193 [main] ERROR com.unimas.ollamaui.client.IntentPredictionClient - Error predicting intent: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"查询订单状态"}}]}
java.io.IOException: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"查询订单状态"}}]}
	at com.unimas.ollamaui.client.RestClient.handleResponse(RestClient.java:189) ~[classes/:?]
	at com.unimas.ollamaui.client.RestClient.post(RestClient.java:91) ~[classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.predictIntent(IntentPredictionClient.java:58) [classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.main(IntentPredictionClient.java:107) [classes/:?]
2025-04-27 09:41:34.194 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:41:34.197 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"message":"帮我写一个Java程序"}
2025-04-27 09:41:34.201 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 422
2025-04-27 09:41:34.201 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"帮我写一个Java程序"}}]}
2025-04-27 09:41:34.202 [main] ERROR com.unimas.ollamaui.client.RestClient - Request failed with status code: 422
2025-04-27 09:41:34.202 [main] ERROR com.unimas.ollamaui.client.IntentPredictionClient - Error predicting intent: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"帮我写一个Java程序"}}]}
java.io.IOException: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"帮我写一个Java程序"}}]}
	at com.unimas.ollamaui.client.RestClient.handleResponse(RestClient.java:189) ~[classes/:?]
	at com.unimas.ollamaui.client.RestClient.post(RestClient.java:91) ~[classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.predictIntent(IntentPredictionClient.java:58) [classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.main(IntentPredictionClient.java:107) [classes/:?]
2025-04-27 09:41:34.202 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:41:34.207 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"message":"SELECT * FROM users"}
2025-04-27 09:41:34.211 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 422
2025-04-27 09:41:34.212 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"SELECT * FROM users"}}]}
2025-04-27 09:41:34.212 [main] ERROR com.unimas.ollamaui.client.RestClient - Request failed with status code: 422
2025-04-27 09:41:34.212 [main] ERROR com.unimas.ollamaui.client.IntentPredictionClient - Error predicting intent: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"SELECT * FROM users"}}]}
java.io.IOException: Request failed with status code: 422, response: {"detail":[{"type":"missing","loc":["body","text"],"msg":"Field required","input":{"message":"SELECT * FROM users"}}]}
	at com.unimas.ollamaui.client.RestClient.handleResponse(RestClient.java:189) ~[classes/:?]
	at com.unimas.ollamaui.client.RestClient.post(RestClient.java:91) ~[classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.predictIntent(IntentPredictionClient.java:58) [classes/:?]
	at com.unimas.ollamaui.client.IntentPredictionClient.main(IntentPredictionClient.java:107) [classes/:?]
2025-04-27 09:45:37.750 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-27 09:45:37.757 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-27 09:45:37.757 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Intent prediction URL: http://10.68.41.41:8000
2025-04-27 09:45:37.762 [main] INFO  com.unimas.ollamaui.client.IntentPredictionClient - Initialized Intent Prediction client with URL: http://10.68.41.41:8000
2025-04-27 09:45:37.766 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:45:38.089 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"1+1等于几"}
2025-04-27 09:45:38.259 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 09:45:38.261 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"1+1等于几","intent":"none"}
2025-04-27 09:45:38.261 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: none
2025-04-27 09:45:38.262 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:45:38.266 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"查询订单状态"}
2025-04-27 09:45:38.405 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 09:45:38.405 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"查询订单状态","intent":"Mid_appInfoSchema"}
2025-04-27 09:45:38.406 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: Mid_appInfoSchema
2025-04-27 09:45:38.406 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:45:38.409 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"帮我写一个Java程序"}
2025-04-27 09:45:38.528 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 09:45:38.529 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"帮我写一个Java程序","intent":"none"}
2025-04-27 09:45:38.530 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: none
2025-04-27 09:45:38.530 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 09:45:38.540 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"SELECT * FROM users"}
2025-04-27 09:45:38.661 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 09:45:38.661 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"SELECT * FROM users","intent":"operateaudit"}
2025-04-27 09:45:38.661 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: operateaudit
2025-04-27 13:39:29.163 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-27 13:39:29.173 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-27 13:39:29.173 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Intent prediction URL: http://10.68.41.41:8000
2025-04-27 13:39:29.177 [main] INFO  com.unimas.ollamaui.client.IntentPredictionClient - Initialized Intent Prediction client with URL: http://10.68.41.41:8000
2025-04-27 13:39:29.179 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 13:39:29.472 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"1+1等于几"}
2025-04-27 13:39:29.632 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 13:39:29.633 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"1+1等于几","intent":"none"}
2025-04-27 13:39:29.635 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: none
2025-04-27 13:39:29.635 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 13:39:29.641 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"查询订单状态"}
2025-04-27 13:39:29.744 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 13:39:29.745 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"查询订单状态","intent":"Mid_appInfoSchema"}
2025-04-27 13:39:29.745 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: Mid_appInfoSchema
2025-04-27 13:39:29.745 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 13:39:29.748 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"帮我写一个Java程序"}
2025-04-27 13:39:29.852 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 13:39:29.852 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"帮我写一个Java程序","intent":"none"}
2025-04-27 13:39:29.852 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: none
2025-04-27 13:39:29.852 [main] DEBUG com.unimas.ollamaui.client.RestClient - Sending POST request to: http://10.68.41.41:8000/predict
2025-04-27 13:39:29.854 [main] DEBUG com.unimas.ollamaui.client.RestClient - Request body: {"text":"SELECT * FROM users"}
2025-04-27 13:39:29.957 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response status code: 200
2025-04-27 13:39:29.957 [main] DEBUG com.unimas.ollamaui.client.RestClient - Response body: {"text":"SELECT * FROM users","intent":"operateaudit"}
2025-04-27 13:39:29.958 [main] DEBUG com.unimas.ollamaui.client.IntentPredictionClient - Predicted intent: operateaudit
