2025-04-24 15:07:44.668 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Ollama UI application
2025-04-24 15:07:44.674 [main] INFO  com.unimas.ollamaui.JettyServer - Java version: 1.8.0_121
2025-04-24 15:07:44.674 [main] INFO  com.unimas.ollamaui.JettyServer - OS: Windows 10 10.0
2025-04-24 15:07:44.675 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-24 15:07:44.676 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-24 15:07:44.676 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Server port: 8080
2025-04-24 15:07:44.676 [main] DEBUG com.unimas.ollamaui.JettyServer - Default port from configuration: 8080
2025-04-24 15:07:44.676 [main] INFO  com.unimas.ollamaui.JettyServer - Creating Jetty server instance on port 8080
2025-04-24 15:07:44.695 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @559ms to org.eclipse.jetty.util.log.Slf4jLog
2025-04-24 15:07:44.740 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Jetty server on port 8080...
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Created ServletContextHandler with context path: /
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting up resource base path...
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Resource 'webapp' not found in classpath, trying filesystem...
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\target\webapp
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:07:44.765 [main] DEBUG com.unimas.ollamaui.JettyServer - Found webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:07:44.769 [main] DEBUG com.unimas.ollamaui.JettyServer - Set base resource to: file:/D:/history/AI/OllamaUI/src/main/webapp/
2025-04-24 15:07:44.770 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering API Servlet at /api/status
2025-04-24 15:07:44.776 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Ollama Proxy Servlet at /proxy/*
2025-04-24 15:07:44.780 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Default Servlet for static resources
2025-04-24 15:07:44.781 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering CORS Filter
2025-04-24 15:07:44.783 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Encoding Filter
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting Ollama configuration parameters
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama API key: [CONFIGURED]
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.JettyServer - Ollama host: ***********, port: 11434
2025-04-24 15:07:44.784 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting server handler
2025-04-24 15:07:44.784 [main] INFO  com.unimas.ollamaui.JettyServer - Starting server...
2025-04-24 15:07:44.787 [main] INFO  org.eclipse.jetty.server.Server - jetty-9.4.46.v20220331; built: 2022-03-31T16:38:08.030Z; git: bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18; jvm 1.8.0_121-b13
2025-04-24 15:07:44.809 [main] INFO  org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-04-24 15:07:44.810 [main] INFO  org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-04-24 15:07:44.811 [main] INFO  org.eclipse.jetty.server.session - node0 Scavenging every 660000ms
2025-04-24 15:07:44.817 [main] DEBUG com.unimas.ollamaui.CorsFilter - Initializing CORS filter
2025-04-24 15:07:44.821 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Initializing OllamaProxyServlet
2025-04-24 15:07:44.822 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Loading configuration from servlet context
2025-04-24 15:07:44.822 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaPort from web.xml: 11434
2025-04-24 15:07:44.822 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaApiKey from web.xml: [CONFIGURED]
2025-04-24 15:07:44.822 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:07:44.822 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler.ROOT - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:07:44.823 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.s.ServletContextHandler@4550bb58{/,file:///D:/history/AI/OllamaUI/src/main/webapp/,AVAILABLE}
2025-04-24 15:07:44.840 [main] INFO  org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@60704c{HTTP/1.1, (http/1.1)}{0.0.0.0:8080}
2025-04-24 15:07:44.840 [main] INFO  org.eclipse.jetty.server.Server - Started @704ms
2025-04-24 15:07:44.840 [main] INFO  com.unimas.ollamaui.JettyServer - 服务器已启动，访问 http://localhost:8080
2025-04-24 15:07:44.840 [main] DEBUG com.unimas.ollamaui.JettyServer - Waiting for server to stop...
2025-04-24 15:08:07.843 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received GET request with path: /api/tags
2025-04-24 15:08:07.844 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/tags
2025-04-24 15:08:08.124 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created GET request to: http://***********:11434/api/tags
2025-04-24 15:08:08.124 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:08:08.258 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:08:08.258 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:08:08.260 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 2740 bytes
2025-04-24 15:08:08.261 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:08:15.596 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:08:15.596 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:08:15.596 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Request body length: 87 bytes
2025-04-24 15:08:15.596 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:08:15.603 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:08:15.603 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:08:15.603 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:08:15.603 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:08:36.609 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:08:36.610 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:08:36.611 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 499 bytes
2025-04-24 15:08:36.613 [qtp2138564891-27] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:08:47.733 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:08:47.733 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:08:47.734 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Request body length: 88 bytes
2025-04-24 15:08:47.734 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:08:47.738 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:08:47.738 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:08:47.738 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:08:47.738 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:08:50.676 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:08:50.676 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:08:50.677 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 382 bytes
2025-04-24 15:08:50.678 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:08:58.043 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:08:58.043 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:08:58.045 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Request body length: 89 bytes
2025-04-24 15:08:58.045 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:08:58.047 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:08:58.047 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:08:58.047 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:08:58.047 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:09:00.178 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:09:00.178 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:09:00.178 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 358 bytes
2025-04-24 15:09:00.179 [qtp2138564891-19] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:09:10.094 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:09:10.094 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:09:10.094 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Request body length: 86 bytes
2025-04-24 15:09:10.094 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:09:10.097 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:09:10.097 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:09:10.097 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:09:10.097 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:09:14.453 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:09:14.453 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:09:14.453 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 410 bytes
2025-04-24 15:09:14.454 [qtp2138564891-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:15:00.859 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Ollama UI application
2025-04-24 15:15:00.864 [main] INFO  com.unimas.ollamaui.JettyServer - Java version: 1.8.0_121
2025-04-24 15:15:00.864 [main] INFO  com.unimas.ollamaui.JettyServer - OS: Windows 10 10.0
2025-04-24 15:15:00.865 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-24 15:15:00.865 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-24 15:15:00.866 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Server port: 8080
2025-04-24 15:15:00.866 [main] DEBUG com.unimas.ollamaui.JettyServer - Default port from configuration: 8080
2025-04-24 15:15:00.866 [main] INFO  com.unimas.ollamaui.JettyServer - Creating Jetty server instance on port 8080
2025-04-24 15:15:00.886 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @545ms to org.eclipse.jetty.util.log.Slf4jLog
2025-04-24 15:15:00.933 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Jetty server on port 8080...
2025-04-24 15:15:00.956 [main] DEBUG com.unimas.ollamaui.JettyServer - Created ServletContextHandler with context path: /
2025-04-24 15:15:00.956 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting up resource base path...
2025-04-24 15:15:00.956 [main] DEBUG com.unimas.ollamaui.JettyServer - Resource 'webapp' not found in classpath, trying filesystem...
2025-04-24 15:15:00.956 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\target\webapp
2025-04-24 15:15:00.956 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:15:00.957 [main] DEBUG com.unimas.ollamaui.JettyServer - Found webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:15:00.961 [main] DEBUG com.unimas.ollamaui.JettyServer - Set base resource to: file:/D:/history/AI/OllamaUI/src/main/webapp/
2025-04-24 15:15:00.961 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering API Servlet at /api/status
2025-04-24 15:15:00.969 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Ollama Proxy Servlet at /proxy/*
2025-04-24 15:15:00.971 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Default Servlet for static resources
2025-04-24 15:15:00.972 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering CORS Filter
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Encoding Filter
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting Ollama configuration parameters
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama API key: [CONFIGURED]
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.JettyServer - Ollama host: ***********, port: 11434
2025-04-24 15:15:00.974 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting server handler
2025-04-24 15:15:00.974 [main] INFO  com.unimas.ollamaui.JettyServer - Starting server...
2025-04-24 15:15:00.978 [main] INFO  org.eclipse.jetty.server.Server - jetty-9.4.46.v20220331; built: 2022-03-31T16:38:08.030Z; git: bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18; jvm 1.8.0_121-b13
2025-04-24 15:15:01.003 [main] INFO  org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-04-24 15:15:01.003 [main] INFO  org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-04-24 15:15:01.006 [main] INFO  org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-04-24 15:15:01.013 [main] DEBUG com.unimas.ollamaui.CorsFilter - Initializing CORS filter
2025-04-24 15:15:01.016 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Initializing OllamaProxyServlet
2025-04-24 15:15:01.016 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Loading configuration from servlet context
2025-04-24 15:15:01.016 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaPort from web.xml: 11434
2025-04-24 15:15:01.016 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaApiKey from web.xml: [CONFIGURED]
2025-04-24 15:15:01.017 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:15:01.017 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler.ROOT - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:15:01.017 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.s.ServletContextHandler@4550bb58{/,file:///D:/history/AI/OllamaUI/src/main/webapp/,AVAILABLE}
2025-04-24 15:15:01.036 [main] INFO  org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@60704c{HTTP/1.1, (http/1.1)}{0.0.0.0:8080}
2025-04-24 15:15:01.036 [main] INFO  org.eclipse.jetty.server.Server - Started @698ms
2025-04-24 15:15:01.036 [main] INFO  com.unimas.ollamaui.JettyServer - 服务器已启动，访问 http://localhost:8080
2025-04-24 15:15:01.036 [main] DEBUG com.unimas.ollamaui.JettyServer - Waiting for server to stop...
2025-04-24 15:15:24.131 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:15:24.131 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:15:24.132 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Request body length: 88 bytes
2025-04-24 15:15:24.132 [qtp2138564891-28] TRACE com.unimas.ollamaui.OllamaProxyServlet - Request body: {"model":"deepseek-r1:7b","messages":[{"role":"user","content":"1+1的值"}],"stream":false}
2025-04-24 15:15:24.132 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:15:24.412 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:15:24.412 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:15:24.413 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:15:24.413 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:15:32.425 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:15:32.425 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:15:32.426 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 361 bytes
2025-04-24 15:15:32.426 [qtp2138564891-28] TRACE com.unimas.ollamaui.OllamaProxyServlet - Response body: {"model":"deepseek-r1:7b","created_at":"2025-04-24T07:15:31.832052215Z","message":{"role":"assistant","content":"\u003cthink\u003e\n\n\u003c/think\u003e\n\n1 + 1 的值是 **2**。"},"done_reason":"stop","done":true,"total_duration":7983327952,"load_duration":5992720276,"prompt_eval_count":8,"prompt_eval_duration":441000000,"eval_count":16,"eval_duration":1547000000}
2025-04-24 15:15:32.430 [qtp2138564891-28] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:28:06.229 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Ollama UI application
2025-04-24 15:28:06.235 [main] INFO  com.unimas.ollamaui.JettyServer - Java version: 1.8.0_121
2025-04-24 15:28:06.235 [main] INFO  com.unimas.ollamaui.JettyServer - OS: Windows 10 10.0
2025-04-24 15:28:06.236 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-24 15:28:06.236 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-24 15:28:06.237 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Server port: 8080
2025-04-24 15:28:06.237 [main] DEBUG com.unimas.ollamaui.JettyServer - Default port from configuration: 8080
2025-04-24 15:28:06.237 [main] INFO  com.unimas.ollamaui.JettyServer - Creating Jetty server instance on port 8080
2025-04-24 15:28:06.257 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @517ms to org.eclipse.jetty.util.log.Slf4jLog
2025-04-24 15:28:06.310 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Jetty server on port 8080...
2025-04-24 15:28:06.333 [main] DEBUG com.unimas.ollamaui.JettyServer - Created ServletContextHandler with context path: /
2025-04-24 15:28:06.333 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting up resource base path...
2025-04-24 15:28:06.333 [main] DEBUG com.unimas.ollamaui.JettyServer - Resource 'webapp' not found in classpath, trying filesystem...
2025-04-24 15:28:06.334 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\target\webapp
2025-04-24 15:28:06.335 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:28:06.335 [main] DEBUG com.unimas.ollamaui.JettyServer - Found webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:28:06.341 [main] DEBUG com.unimas.ollamaui.JettyServer - Set base resource to: file:/D:/history/AI/OllamaUI/src/main/webapp/
2025-04-24 15:28:06.341 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering API Servlet at /api/status
2025-04-24 15:28:06.351 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Ollama Proxy Servlet at /proxy/*
2025-04-24 15:28:06.355 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Default Servlet for static resources
2025-04-24 15:28:06.356 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering CORS Filter
2025-04-24 15:28:06.357 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Encoding Filter
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting Ollama configuration parameters
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama API key: [CONFIGURED]
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.JettyServer - Ollama host: ***********, port: 11434
2025-04-24 15:28:06.358 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting server handler
2025-04-24 15:28:06.359 [main] INFO  com.unimas.ollamaui.JettyServer - Starting server...
2025-04-24 15:28:06.361 [main] INFO  org.eclipse.jetty.server.Server - jetty-9.4.46.v20220331; built: 2022-03-31T16:38:08.030Z; git: bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18; jvm 1.8.0_121-b13
2025-04-24 15:28:06.386 [main] INFO  org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-04-24 15:28:06.386 [main] INFO  org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-04-24 15:28:06.387 [main] INFO  org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-04-24 15:28:06.395 [main] DEBUG com.unimas.ollamaui.CorsFilter - Initializing CORS filter
2025-04-24 15:28:06.399 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Initializing OllamaProxyServlet
2025-04-24 15:28:06.399 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Loading configuration from servlet context
2025-04-24 15:28:06.399 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaPort from web.xml: 11434
2025-04-24 15:28:06.399 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaApiKey from web.xml: [CONFIGURED]
2025-04-24 15:28:06.399 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:28:06.399 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler.ROOT - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:28:06.399 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.s.ServletContextHandler@6adbc9d{/,file:///D:/history/AI/OllamaUI/src/main/webapp/,AVAILABLE}
2025-04-24 15:28:06.417 [main] INFO  org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@1d082e88{HTTP/1.1, (http/1.1)}{0.0.0.0:8080}
2025-04-24 15:28:06.417 [main] INFO  org.eclipse.jetty.server.Server - Started @679ms
2025-04-24 15:28:06.418 [main] INFO  com.unimas.ollamaui.JettyServer - 服务器已启动，访问 http://localhost:8080
2025-04-24 15:28:06.418 [main] DEBUG com.unimas.ollamaui.JettyServer - Waiting for server to stop...
2025-04-24 15:28:11.784 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received GET request with path: /api/tags
2025-04-24 15:28:11.785 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/tags
2025-04-24 15:28:12.080 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created GET request to: http://***********:11434/api/tags
2025-04-24 15:28:12.080 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:28:12.116 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:28:12.116 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:28:12.118 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 2740 bytes
2025-04-24 15:28:12.119 [qtp439928219-21] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:28:19.079 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received POST request with path: /api/chat
2025-04-24 15:28:19.079 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/chat
2025-04-24 15:28:19.087 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Built JSON request in backend: {"stream":false,"messages":[{"role":"user","content":"你是谁"}],"model":"deepseek-r1:7b"}
2025-04-24 15:28:19.087 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Creating HTTP client
2025-04-24 15:28:19.092 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created POST request with Content-Type: application/json
2025-04-24 15:28:19.092 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:28:19.093 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set request entity with UTF-8 encoding
2025-04-24 15:28:19.093 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Executing request to Ollama
2025-04-24 15:28:29.422 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:28:29.422 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:28:29.422 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 413 bytes
2025-04-24 15:28:29.422 [qtp439928219-25] TRACE com.unimas.ollamaui.OllamaProxyServlet - Response body: {"model":"deepseek-r1:7b","created_at":"2025-04-24T07:28:28.829105864Z","message":{"role":"assistant","content":"\u003cthink\u003e\n\n\u003c/think\u003e\n\n您好！我是由中国的深度求索（DeepSeek）公司开发的智能助手DeepSeek-R1。如您有任何任何问题，我会尽我所能为您提供帮助。"},"done_reason":"stop","done":true,"total_duration":10325769476,"load_duration":5993776243,"prompt_eval_count":5,"prompt_eval_duration":309000000,"eval_count":40,"eval_duration":4010000000}
2025-04-24 15:28:29.422 [qtp439928219-25] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
2025-04-24 15:32:02.297 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Ollama UI application
2025-04-24 15:32:02.304 [main] INFO  com.unimas.ollamaui.JettyServer - Java version: 1.8.0_121
2025-04-24 15:32:02.304 [main] INFO  com.unimas.ollamaui.JettyServer - OS: Windows 10 10.0
2025-04-24 15:32:02.305 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-24 15:32:02.306 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-24 15:32:02.306 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Server port: 8080
2025-04-24 15:32:02.306 [main] DEBUG com.unimas.ollamaui.JettyServer - Default port from configuration: 8080
2025-04-24 15:32:02.306 [main] INFO  com.unimas.ollamaui.JettyServer - Creating Jetty server instance on port 8080
2025-04-24 15:32:02.324 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @544ms to org.eclipse.jetty.util.log.Slf4jLog
2025-04-24 15:32:02.373 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Jetty server on port 8080...
2025-04-24 15:32:02.395 [main] DEBUG com.unimas.ollamaui.JettyServer - Created ServletContextHandler with context path: /
2025-04-24 15:32:02.396 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting up resource base path...
2025-04-24 15:32:02.396 [main] DEBUG com.unimas.ollamaui.JettyServer - Resource 'webapp' not found in classpath, trying filesystem...
2025-04-24 15:32:02.396 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\target\webapp
2025-04-24 15:32:02.396 [main] DEBUG com.unimas.ollamaui.JettyServer - Checking for webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:32:02.396 [main] DEBUG com.unimas.ollamaui.JettyServer - Found webapp directory at: D:\history\AI\OllamaUI\src\main\webapp
2025-04-24 15:32:02.402 [main] DEBUG com.unimas.ollamaui.JettyServer - Set base resource to: file:/D:/history/AI/OllamaUI/src/main/webapp/
2025-04-24 15:32:02.402 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering API Servlet at /api/status
2025-04-24 15:32:02.410 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Ollama Proxy Servlet at /proxy/*
2025-04-24 15:32:02.411 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Default Servlet for static resources
2025-04-24 15:32:02.412 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering CORS Filter
2025-04-24 15:32:02.414 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Encoding Filter
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting Ollama configuration parameters
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama API key: [CONFIGURED]
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.JettyServer - Ollama host: ***********, port: 11434
2025-04-24 15:32:02.415 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting server handler
2025-04-24 15:32:02.415 [main] INFO  com.unimas.ollamaui.JettyServer - Starting server...
2025-04-24 15:32:02.418 [main] INFO  org.eclipse.jetty.server.Server - jetty-9.4.46.v20220331; built: 2022-03-31T16:38:08.030Z; git: bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18; jvm 1.8.0_121-b13
2025-04-24 15:32:02.448 [main] INFO  org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-04-24 15:32:02.448 [main] INFO  org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-04-24 15:32:02.449 [main] INFO  org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-04-24 15:32:02.458 [main] DEBUG com.unimas.ollamaui.CorsFilter - Initializing CORS filter
2025-04-24 15:32:02.464 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Initializing OllamaProxyServlet
2025-04-24 15:32:02.464 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Loading configuration from servlet context
2025-04-24 15:32:02.464 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaPort from web.xml: 11434
2025-04-24 15:32:02.464 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaApiKey from web.xml: [CONFIGURED]
2025-04-24 15:32:02.464 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:32:02.464 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler.ROOT - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:32:02.465 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.s.ServletContextHandler@4550bb58{/,file:///D:/history/AI/OllamaUI/src/main/webapp/,AVAILABLE}
2025-04-24 15:32:02.485 [main] INFO  org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@60704c{HTTP/1.1, (http/1.1)}{0.0.0.0:8080}
2025-04-24 15:32:02.485 [main] INFO  org.eclipse.jetty.server.Server - Started @705ms
2025-04-24 15:32:02.485 [main] INFO  com.unimas.ollamaui.JettyServer - 服务器已启动，访问 http://localhost:8080
2025-04-24 15:32:02.485 [main] DEBUG com.unimas.ollamaui.JettyServer - Waiting for server to stop...
2025-04-24 15:48:36.528 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Ollama UI application
2025-04-24 15:48:36.534 [main] INFO  com.unimas.ollamaui.JettyServer - Java version: 1.8.0_121
2025-04-24 15:48:36.534 [main] INFO  com.unimas.ollamaui.JettyServer - OS: Windows 10 10.0
2025-04-24 15:48:36.535 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Loading configuration from application.properties
2025-04-24 15:48:36.535 [main] INFO  com.unimas.ollamaui.config.AppConfig - 成功加载配置文件: application.properties
2025-04-24 15:48:36.535 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Server port: 8080
2025-04-24 15:48:36.535 [main] DEBUG com.unimas.ollamaui.JettyServer - Default port from configuration: 8080
2025-04-24 15:48:36.535 [main] INFO  com.unimas.ollamaui.JettyServer - Creating Jetty server instance on port 8080
2025-04-24 15:48:36.554 [main] INFO  org.eclipse.jetty.util.log - Logging initialized @514ms to org.eclipse.jetty.util.log.Slf4jLog
2025-04-24 15:48:36.604 [main] INFO  com.unimas.ollamaui.JettyServer - Starting Jetty server on port 8080...
2025-04-24 15:48:36.629 [main] DEBUG com.unimas.ollamaui.JettyServer - Created ServletContextHandler with context path: /
2025-04-24 15:48:36.629 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting up resource base path...
2025-04-24 15:48:36.629 [main] DEBUG com.unimas.ollamaui.JettyServer - Found webapp resource in classpath
2025-04-24 15:48:36.634 [main] DEBUG com.unimas.ollamaui.JettyServer - Set base resource to: file:/D:/history/AI/OllamaUI/target/classes/webapp
2025-04-24 15:48:36.634 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering API Servlet at /api/status
2025-04-24 15:48:36.640 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Ollama Proxy Servlet at /proxy/*
2025-04-24 15:48:36.644 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Default Servlet for static resources
2025-04-24 15:48:36.645 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering CORS Filter
2025-04-24 15:48:36.647 [main] DEBUG com.unimas.ollamaui.JettyServer - Registering Encoding Filter
2025-04-24 15:48:36.647 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting Ollama configuration parameters
2025-04-24 15:48:36.647 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama API key: [CONFIGURED]
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama host: ***********
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.config.AppConfig - Ollama port: 11434
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.JettyServer - Ollama host: ***********, port: 11434
2025-04-24 15:48:36.648 [main] DEBUG com.unimas.ollamaui.JettyServer - Setting server handler
2025-04-24 15:48:36.648 [main] INFO  com.unimas.ollamaui.JettyServer - Starting server...
2025-04-24 15:48:36.650 [main] INFO  org.eclipse.jetty.server.Server - jetty-9.4.46.v20220331; built: 2022-03-31T16:38:08.030Z; git: bc17a0369a11ecf40bb92c839b9ef0a8ac50ea18; jvm 1.8.0_121-b13
2025-04-24 15:48:36.679 [main] INFO  org.eclipse.jetty.server.session - DefaultSessionIdManager workerName=node0
2025-04-24 15:48:36.679 [main] INFO  org.eclipse.jetty.server.session - No SessionScavenger set, using defaults
2025-04-24 15:48:36.680 [main] INFO  org.eclipse.jetty.server.session - node0 Scavenging every 600000ms
2025-04-24 15:48:36.688 [main] DEBUG com.unimas.ollamaui.CorsFilter - Initializing CORS filter
2025-04-24 15:48:36.693 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Initializing OllamaProxyServlet
2025-04-24 15:48:36.693 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Loading configuration from servlet context
2025-04-24 15:48:36.693 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaPort from web.xml: 11434
2025-04-24 15:48:36.693 [main] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Using ollamaApiKey from web.xml: [CONFIGURED]
2025-04-24 15:48:36.694 [main] INFO  com.unimas.ollamaui.OllamaProxyServlet - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:48:36.694 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler.ROOT - Ollama代理配置 - 主机: ***********, 端口: 11434
2025-04-24 15:48:36.694 [main] INFO  org.eclipse.jetty.server.handler.ContextHandler - Started o.e.j.s.ServletContextHandler@4550bb58{/,file:///D:/history/AI/OllamaUI/target/classes/webapp/,AVAILABLE}
2025-04-24 15:48:36.715 [main] INFO  org.eclipse.jetty.server.AbstractConnector - Started ServerConnector@60704c{HTTP/1.1, (http/1.1)}{0.0.0.0:8080}
2025-04-24 15:48:36.716 [main] INFO  org.eclipse.jetty.server.Server - Started @677ms
2025-04-24 15:48:36.716 [main] INFO  com.unimas.ollamaui.JettyServer - 服务器已启动，访问 http://localhost:8080
2025-04-24 15:48:36.716 [main] DEBUG com.unimas.ollamaui.JettyServer - Waiting for server to stop...
2025-04-24 15:48:40.584 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received GET request with path: /api/tags
2025-04-24 15:48:40.584 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Forwarding to Ollama URL: http://***********:11434/api/tags
2025-04-24 15:48:40.865 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Created GET request to: http://***********:11434/api/tags
2025-04-24 15:48:40.865 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Added Authorization header
2025-04-24 15:48:40.898 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Ollama response status code: 200
2025-04-24 15:48:40.898 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Set response content type to application/json and charset to UTF-8
2025-04-24 15:48:40.900 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Received response from Ollama, length: 2740 bytes
2025-04-24 15:48:40.901 [qtp2138564891-18] DEBUG com.unimas.ollamaui.OllamaProxyServlet - Response sent to client
