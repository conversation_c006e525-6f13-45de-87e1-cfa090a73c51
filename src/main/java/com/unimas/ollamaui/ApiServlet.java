package com.unimas.ollamaui;

import com.unimas.ollamaui.config.AppConfig;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * API端点，提供系统信息和配置
 */
@WebServlet("/api/status")
public class ApiServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 设置响应类型为JSON
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // 创建JSON响应
        JSONObject jsonResponse = new JSONObject();
        jsonResponse.put("status", "ok");
        jsonResponse.put("message", "服务器正常工作");
        jsonResponse.put("timestamp", System.currentTimeMillis());

        // 添加配置信息（不包含敏感信息如API密钥）
        JSONObject configInfo = new JSONObject();
        configInfo.put("ollamaHost", AppConfig.getOllamaHost());
        configInfo.put("ollamaPort", AppConfig.getOllamaPort());
        configInfo.put("serverPort", AppConfig.getServerPort());

        jsonResponse.put("config", configInfo);

        // 发送响应
        PrintWriter out = response.getWriter();
        out.print(jsonResponse.toString());
        out.flush();
    }
}
