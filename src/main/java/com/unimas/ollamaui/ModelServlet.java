package com.unimas.ollamaui;

import com.unimas.ollamaui.config.AppConfig;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * 模型管理Servlet，专门处理模型相关的API请求
 * 主要处理 /api/tags 请求，获取Ollama服务器上的可用模型列表
 */
@WebServlet("/api/tags")
public class ModelServlet extends HttpServlet {
    private static final Logger logger = LogManager.getLogger(ModelServlet.class);
    private static final long serialVersionUID = 1L;

    private String ollamaHost;
    private int ollamaPort;
    private String ollamaApiKey;

    @Override
    public void init() throws ServletException {
        super.init();
        logger.info("Initializing ModelServlet");

        // 从servlet context获取配置
        logger.debug("Loading configuration from servlet context");
        ollamaHost = getServletContext().getInitParameter("ollamaHost");
        String ollamaPortStr = getServletContext().getInitParameter("ollamaPort");
        ollamaApiKey = getServletContext().getInitParameter("ollamaApiKey");

        // 如果servlet context中未设置，则使用配置文件中的值
        if (ollamaHost == null || ollamaHost.isEmpty()) {
            logger.debug("ollamaHost not set in servlet context, using value from AppConfig");
            ollamaHost = AppConfig.getOllamaHost();
        }

        if (ollamaPortStr == null || ollamaPortStr.isEmpty()) {
            logger.debug("ollamaPort not set in servlet context, using value from AppConfig");
            ollamaPort = AppConfig.getOllamaPort();
        } else {
            try {
                ollamaPort = Integer.parseInt(ollamaPortStr);
                logger.debug("Using ollamaPort from servlet context: {}", ollamaPort);
            } catch (NumberFormatException e) {
                logger.warn("无效的Ollama端口配置: {}, 使用配置文件中的值: {}", ollamaPortStr, AppConfig.getOllamaPort(), e);
                ollamaPort = AppConfig.getOllamaPort();
            }
        }

        if (ollamaApiKey == null || ollamaApiKey.isEmpty()) {
            logger.debug("ollamaApiKey not set in servlet context, using value from AppConfig");
            ollamaApiKey = AppConfig.getOllamaApiKey();
        } else {
            logger.debug("Using ollamaApiKey from servlet context: [CONFIGURED]");
        }

        logger.info("ModelServlet配置 - Ollama主机: {}, 端口: {}", ollamaHost, ollamaPort);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        logger.debug("Received GET request for model list");

        // 构建Ollama API URL
        String ollamaUrl = "http://" + ollamaHost + ":" + ollamaPort + "/api/tags";
        logger.debug("Forwarding request to Ollama URL: {}", ollamaUrl);

        // 设置响应头
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // 创建HTTP客户端
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建GET请求
            HttpGet httpGet = new HttpGet(ollamaUrl);
            logger.debug("Created GET request to: {}", ollamaUrl);

            // 添加API密钥（如果有）
            if (ollamaApiKey != null && !ollamaApiKey.isEmpty()) {
                httpGet.setHeader("Authorization", "Bearer " + ollamaApiKey);
                logger.debug("Added Authorization header");
            }

            // 执行请求
            try (CloseableHttpResponse ollamaResponse = httpClient.execute(httpGet)) {
                // 获取响应状态码
                int statusCode = ollamaResponse.getStatusLine().getStatusCode();
                response.setStatus(statusCode);
                logger.debug("Ollama response status code: {}", statusCode);

                // 获取响应内容
                HttpEntity entity = ollamaResponse.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    logger.debug("Received response from Ollama, length: {} bytes", result.length());
                    
                    if (logger.isTraceEnabled()) {
                        logger.trace("Response body: {}", result);
                    }

                    // 发送响应给客户端
                    PrintWriter out = response.getWriter();
                    out.print(result);
                    out.flush();
                    logger.debug("Model list response sent to client");
                } else {
                    logger.warn("Received empty response from Ollama");
                    response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                    PrintWriter out = response.getWriter();
                    out.print("{\"models\":[]}");
                    out.flush();
                }
            }
        } catch (Exception e) {
            logger.error("Error forwarding request to Ollama: {}", e.getMessage(), e);
            
            // 返回错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            PrintWriter out = response.getWriter();
            out.print("{\"error\":\"Failed to connect to Ollama server: " + e.getMessage() + "\"}");
            out.flush();
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // /api/tags 通常只支持GET请求，但为了完整性也实现POST
        logger.debug("Received POST request for model list, delegating to GET handler");
        doGet(request, response);
    }

    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 处理CORS预检请求
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
        response.setStatus(HttpServletResponse.SC_OK);
        logger.debug("Handled OPTIONS request for CORS");
    }
}
