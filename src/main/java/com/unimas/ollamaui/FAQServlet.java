package com.unimas.ollamaui;


import com.unimas.ollamaui.client.FAQPredictionClient;
import com.unimas.ollamaui.config.AppConfig;
import com.unimas.ollamaui.db.DatabaseManager;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Ollama代理Servlet，用于转发请求到Ollama服务器
 */
@WebServlet("/faq/*")
public class FAQServlet extends HttpServlet {
    private static final Logger logger = LogManager.getLogger(FAQServlet.class);
    private static final long serialVersionUID = 1L;

    private String ollamaHost;
    private int ollamaPort;
    private String ollamaApiKey;

    @Override
    public void init() throws ServletException {
        super.init();
        logger.info("Initializing OllamaProxyServlet");

        // 从web.xml或环境变量获取配置
        logger.debug("Loading configuration from servlet context");
        ollamaHost = getServletContext().getInitParameter("ollamaHost");
        String ollamaPortStr = getServletContext().getInitParameter("ollamaPort");
        ollamaApiKey = getServletContext().getInitParameter("ollamaApiKey");

        // 如果web.xml中未设置，则使用配置文件中的值
        if (ollamaHost == null || ollamaHost.isEmpty()) {
            logger.debug("ollamaHost not set in web.xml, using value from AppConfig");
            ollamaHost = AppConfig.getOllamaHost();
        }

        if (ollamaPortStr == null || ollamaPortStr.isEmpty()) {
            logger.debug("ollamaPort not set in web.xml, using value from AppConfig");
            ollamaPort = AppConfig.getOllamaPort();
        } else {
            try {
                ollamaPort = Integer.parseInt(ollamaPortStr);
                logger.debug("Using ollamaPort from web.xml: {}", ollamaPort);
            } catch (NumberFormatException e) {
                logger.warn("无效的Ollama端口配置: {}, 使用配置文件中的值: {}", ollamaPortStr, AppConfig.getOllamaPort(), e);
                getServletContext().log("无效的Ollama端口配置，使用配置文件中的值: " + AppConfig.getOllamaPort());
                ollamaPort = AppConfig.getOllamaPort();
            }
        }

        if (ollamaApiKey == null || ollamaApiKey.isEmpty()) {
            logger.debug("ollamaApiKey not set in web.xml, using value from AppConfig");
            ollamaApiKey = AppConfig.getOllamaApiKey();
        } else {
            logger.debug("Using ollamaApiKey from web.xml: [CONFIGURED]");
        }

        logger.info("Ollama代理配置 - 主机: {}, 端口: {}", ollamaHost, ollamaPort);
        getServletContext().log("Ollama代理配置 - 主机: " + ollamaHost + ", 端口: " + ollamaPort);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 获取请求路径
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "";
        }
        logger.debug("Received GET request with path: {}", pathInfo);

        // 构建Ollama API URL
        String ollamaUrl = "http://" + ollamaHost + ":" + ollamaPort + pathInfo;
        logger.debug("Forwarding to Ollama URL: {}", ollamaUrl);

        // 创建HTTP客户端
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建GET请求
            HttpGet httpGet = new HttpGet(ollamaUrl);
            logger.debug("Created GET request to: {}", ollamaUrl);

            // 添加API密钥（如果有）
            if (ollamaApiKey != null && !ollamaApiKey.isEmpty()) {
                httpGet.setHeader("Authorization", "Bearer " + ollamaApiKey);
                logger.debug("Added Authorization header");
            }

            // 执行请求
            try (CloseableHttpResponse ollamaResponse = httpClient.execute(httpGet)) {
                // 设置响应状态码
                int statusCode = ollamaResponse.getStatusLine().getStatusCode();
                response.setStatus(statusCode);
                logger.debug("Ollama response status code: {}", statusCode);

                // 设置响应头
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                logger.debug("Set response content type to application/json and charset to UTF-8");

                // 获取响应内容
                HttpEntity entity = ollamaResponse.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    logger.debug("Received response from Ollama, length: {} bytes", result.length());

                    // 发送响应
                    PrintWriter out = response.getWriter();
                    out.print(result);
                    out.flush();
                    logger.debug("Response sent to client");
                } else {
                    logger.warn("Received empty response from Ollama");
                }
            }
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 获取请求路径
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "";
        }
        logger.debug("Received POST request with path: {}", pathInfo);

        // 构建Ollama API URL
        String ollamaUrl = "http://" + ollamaHost + ":" + ollamaPort + pathInfo;
        logger.debug("Forwarding to Ollama URL: {}", ollamaUrl);

        // 准备请求JSON
        String requestBodyStr;

        // 如果是聊天API路径，则在后端构建JSON
        if (pathInfo.endsWith("/api/chat") || pathInfo.equals("/api/chat")) {
            // 从请求参数中获取模型和提示词
            String model = request.getParameter("model");
            String prompt = request.getParameter("prompt");
            boolean stream = Boolean.parseBoolean(request.getParameter("stream"));

            // 如果未提供模型参数，使用默认模型
            if (model == null || model.isEmpty()) {
                model = "llama2"; // 设置默认模型
                logger.info("No model specified, using default model: {}", model);
            }

            if (prompt == null || prompt.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Missing prompt parameter\"}");
                return;
            }
            //重新构建prompt
            FAQPredictionClient client = new FAQPredictionClient();
            JSONArray intents = client.predictIntent(prompt);
//            String fieldName = PythonScriptExecutor.predictIntent(prompt);
            logger.info("Predicted intents: {}", intents);

            // 根据意图数组从FAQ表中查询对应的answers
            JSONArray faqResults = new JSONArray();
            String promptTemplate = null;

            if (intents != null && intents.length() > 0) {
                // 遍历所有意图，查询对应的答案
                for (int i = 0; i < intents.length(); i++) {
                    String fieldName = intents.getString(i);
                    if (fieldName != null && !fieldName.isEmpty()) {
                        String result = readPromptTemplate(fieldName);
                        if (result != null && !result.isEmpty()) {
                            // 创建包含字段名和结果的JSON对象
                            JSONObject faqResult = new JSONObject();
//                            faqResult.put("intent", fieldName);
                            faqResult.put("content" + i, result);
                            faqResults.put(faqResult);

//                            // 如果是第一个结果，设置为主要模板
//                            if (i == 0) {
//                                promptTemplate = result;
//                                logger.info("Using primary prompt template for field: {}", fieldName);
//                            }
                        }
                    }
                }
            }
            String processedTemplate = "";
            // 如果找到了模板，直接返回给页面，不请求Ollama
            if (!faqResults.toString().isEmpty()) {
                // 替换模板中的{prompt}为用户输入
                processedTemplate = "根据以下内容：\n\n" + faqResults.toString() + "\n\n回答用户的问题：" + prompt;
                logger.debug("Using template from FAQ answer directly: {}", processedTemplate);

                // 处理模板中的图片路径
                JSONArray images = new JSONArray();
                processedTemplate = processImagePaths(processedTemplate, images);

//                // 创建响应JSON对象
//                JSONObject responseJson = new JSONObject();
//                JSONObject messageObj = new JSONObject();
//                messageObj.put("content", processedTemplate);
//                messageObj.put("role", "assistant");
//
//                // 如果有图片，添加到响应中
//                if (images.length() > 0) {
//                    messageObj.put("images", images);
//                }
//
//                responseJson.put("message", messageObj);
//
//                // 添加所有FAQ结果到响应中
//                if (faqResults.length() > 0) {
//                    responseJson.put("faq_results", faqResults);
//                }

//                // 设置响应头
//                response.setContentType("application/json; charset=UTF-8");
//                response.setCharacterEncoding("UTF-8");
//                logger.debug("Set response content type to application/json and charset to UTF-8");
//
//                response.getWriter().write(responseJson.toString());
//                response.getWriter().flush();
//
//                logger.debug("Direct FAQ answer sent to client");
//                return; // 直接返回，不请求Ollama
            }

            // 如果没有找到模板或模板为空，则继续请求Ollama
            // 构建JSON请求
            JSONObject requestJson = new JSONObject();
            requestJson.put("model", model);

            JSONArray messages = new JSONArray();
            JSONObject message = new JSONObject();
            message.put("role", "user");
            message.put("content", processedTemplate);
            messages.put(message);

            requestJson.put("messages", messages);
            requestJson.put("stream", stream);

            requestBodyStr = requestJson.toString();
            logger.debug("Built JSON request in backend: {}", requestBodyStr);
        } else {
            // 其他API路径，直接读取请求体
            StringBuilder requestBody = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    requestBody.append(line);
                }
            }
            requestBodyStr = requestBody.toString();
            logger.debug("Using client-provided request body, length: {} bytes", requestBodyStr.length());
            if (logger.isTraceEnabled()) {
                logger.trace("Request body: {}", requestBodyStr);
            }
        }

        // 创建HTTP客户端
        logger.debug("Creating HTTP client");
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建POST请求
            HttpPost httpPost = new HttpPost(ollamaUrl);
            httpPost.setHeader("Content-Type", "application/json");
            logger.debug("Created POST request with Content-Type: application/json");

            // 添加API密钥（如果有）
            if (ollamaApiKey != null && !ollamaApiKey.isEmpty()) {
                httpPost.setHeader("Authorization", "Bearer " + ollamaApiKey);
                logger.debug("Added Authorization header");
            }

            // 设置请求体
            httpPost.setEntity(new StringEntity(requestBodyStr, StandardCharsets.UTF_8));
            logger.debug("Set request entity with UTF-8 encoding");

            // 执行请求
            logger.debug("Executing request to Ollama");
            try (CloseableHttpResponse ollamaResponse = httpClient.execute(httpPost)) {
                // 设置响应状态码
                int statusCode = ollamaResponse.getStatusLine().getStatusCode();
                response.setStatus(statusCode);
                logger.debug("Ollama response status code: {}", statusCode);

                // 检查是否是流式请求
                boolean isStreamRequest = Boolean.parseBoolean(request.getParameter("stream"));

                if (isStreamRequest) {
                    // 流式响应处理
                    handleStreamingResponse(ollamaResponse, response);
                } else {
                    // 非流式响应处理（保持原有逻辑）
                    handleNonStreamingResponse(ollamaResponse, response);
                }
            }
        }
    }

    /**
     * 处理流式响应
     */
    private void handleStreamingResponse(CloseableHttpResponse ollamaResponse, HttpServletResponse response) throws IOException {
        logger.debug("Processing streaming response");

        // 设置流式响应头
        response.setContentType("text/plain; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");

        HttpEntity entity = ollamaResponse.getEntity();
        if (entity != null) {
            try (InputStream inputStream = entity.getContent();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                 PrintWriter out = response.getWriter()) {

                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        try {
                            // 解析每行JSON
                            JSONObject jsonLine = new JSONObject(line);

                            // 提取content字段
                            if (jsonLine.has("message") && jsonLine.getJSONObject("message").has("content")) {
                                String content = jsonLine.getJSONObject("message").getString("content");

                                // 过滤掉<think>标签内容
                                if (content.contains("<think>")) {
                                    content = content.replaceAll("<think>.*?</think>", "");
                                }

                                // 发送内容到前端
                                if (!content.isEmpty()) {
                                    out.print(content);
                                    out.flush();
                                    logger.debug("Sent streaming content: [{}]", content);
                                }
                            }

                            // 检查是否是最后一行（done: true）
                            if (jsonLine.optBoolean("done", false)) {
                                logger.debug("Streaming completed");
                                break;
                            }
                        } catch (Exception e) {
                            logger.debug("Failed to parse JSON line: {}, error: {}", line, e.getMessage());
                        }
                    }
                }
            }
        } else {
            logger.warn("Received empty streaming response from Ollama");
        }
    }

    /**
     * 处理非流式响应（保持原有逻辑）
     */
    private void handleNonStreamingResponse(CloseableHttpResponse ollamaResponse, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        logger.debug("Set response content type to application/json and charset to UTF-8");

        // 获取响应内容
        HttpEntity entity = ollamaResponse.getEntity();
        if (entity != null) {
            String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            logger.debug("Received response from Ollama, length: {} bytes", result.length());
            if (logger.isTraceEnabled()) {
                logger.trace("Response body: {}", result);
            }

            // 处理流式返回的多行JSON
            JSONObject mergedResponse = mergeStreamingJsonLines(result);

            try {
                // 检查是否包含 message 和 content 字段
                if (mergedResponse.has("message") && mergedResponse.getJSONObject("message").has("content")) {
                    // 提取content字段中的内容
                    String content = mergedResponse.getJSONObject("message").getString("content").trim();
                    logger.debug("提取到合并后的content内容: {}", content);
                    //TODO去掉content中<think></think>及其包含的内容
                    if (content.contains("<think>")) {
                        content = content.replaceAll("<think>.*?</think>", "");
                    }
                        // 发送组合结果给前端
                        PrintWriter out = response.getWriter();
                        out.print(content);
                        out.flush();
                        logger.debug("SQL查询结果已发送给客户端");
                        return; // 已处理完毕，返回

                }

                // 如果不是SQL查询或无法解析，直接返回原始结果
                PrintWriter out = response.getWriter();
                out.print(result);
                out.flush();
                logger.debug("Response sent to client");
            } catch (Exception e) {
                // JSON解析错误或其他异常，直接返回原始结果
                logger.warn("处理响应时出错: {}", e.getMessage());
                PrintWriter out = response.getWriter();
                out.print(result);
                out.flush();
                logger.debug("Response sent to client (after error)");
            }
        } else {
            logger.warn("Received empty response from Ollama");
        }
    }

    /**
     * 根据字段名从FAQ表中查询对应的answer字段值
     *
     * @param fieldName 字段名（列名）
     * @return 对应的answer值，如果查询失败则返回null
     */
    private String readPromptTemplate(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return null;
        }else{
            fieldName = "__rag__"+ fieldName;
        }

        logger.debug("Querying FAQ table for field: {}", fieldName);

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT qid,question,answer FROM faq WHERE qid = ?")) {

            stmt.setString(1, fieldName);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    // 获取qid和question字段
                    String qid = rs.getString("qid");
                    byte[] questionBytes = rs.getBytes("question");
                    String question = new String(questionBytes, "gbk");

                    // 使用getBytes获取原始字节，然后使用UTF-8编码创建字符串
                    byte[] answerBytes = rs.getBytes("answer");
                    String answer = new String(answerBytes, "gbk");

                    // 构建包含qid和question的完整答案
                    StringBuilder fullAnswer = new StringBuilder();
                    fullAnswer.append("问题ID: ").append(qid).append("\n");
                    fullAnswer.append("问题: ").append(question).append("\n\n");
                    fullAnswer.append(answer);

                    logger.debug("Found answer for field '{}' in FAQ table", fieldName);
                    logger.debug("QID: {}, Question: {}", qid, question);

                    return fullAnswer.toString();
                } else {
                    logger.warn("No answer found for field '{}' in FAQ table", fieldName);
                    return null;
                }
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        } catch (SQLException e) {
            logger.error("Error querying FAQ table for field '{}': {}", fieldName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 判断字符串是否是SQL查询语句
     *
     * @param text 要检查的文本
     * @return 如果是SQL查询语句则返回true，否则返回false
     */
    private boolean isSqlQuery(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 去除前后空白
        String trimmedText = text.trim();

        // 检查是否以SQL关键字开头
        String lowerText = trimmedText.toLowerCase();

        // 检查是否是SELECT语句
        if (lowerText.startsWith("select")) {
            logger.debug("检测到SELECT语句");
            return true;
        }

        // 检查是否包含常见SQL关键字组合
        String[] sqlPatterns = {
            "from\\s+[\\w_]+\\s+where",
            "select\\s+[\\w\\*,_\\s]+\\s+from",
            "join\\s+[\\w_]+\\s+on",
            "group\\s+by\\s+[\\w_]+",
            "order\\s+by\\s+[\\w_]+"
        };

        for (String pattern : sqlPatterns) {
            if (lowerText.matches(".*" + pattern + ".*")) {
                logger.debug("检测到SQL模式: {}", pattern);
                return true;
            }
        }

        // 如果文本很短且包含SELECT，也认为是SQL
        if (trimmedText.length() < 200 && lowerText.contains("select") && lowerText.contains("from")) {
            logger.debug("检测到简短的SELECT查询");
            return true;
        }

        return false;
    }

    /**
     * 处理流式返回的多行JSON，合并所有行的content字段，并保留最后一行的其他字段
     *
     * @param result 原始响应字符串，包含多行JSON
     * @return 合并后的JSONObject
     */
    private JSONObject mergeStreamingJsonLines(String result) {
        if (result == null || result.isEmpty()) {
            return new JSONObject();
        }

        logger.debug("开始处理流式返回的多行JSON");

        // 按行分割内容
        String[] lines = result.split("\n");
        StringBuilder mergedContent = new StringBuilder();
        JSONObject lastJsonLine = null;

        for (String line : lines) {
            try {
                // 检查是否是JSON格式
                if (line.trim().startsWith("{") && line.trim().endsWith("}")) {
                    JSONObject jsonLine = new JSONObject(line);

                    // 如果包含message和content字段，提取content并添加到合并内容中
                    if (jsonLine.has("message") && jsonLine.getJSONObject("message").has("content")) {
                        String content = jsonLine.getJSONObject("message").getString("content");
                        mergedContent.append(content);
                        logger.debug("从行JSON中提取并添加content: [{}]", content);
                    }

                    // 保存最后一行JSON，用于保留其他字段
                    lastJsonLine = jsonLine;
                }
            } catch (Exception e) {
                logger.debug("解析行为JSON失败: {}", e.getMessage());
            }
        }

        // 如果没有有效的JSON行，返回空对象
        if (lastJsonLine == null) {
            logger.warn("没有找到有效的JSON行");
            return new JSONObject();
        }

        // 创建合并后的响应对象，使用最后一行的其他字段
        JSONObject mergedResponse = new JSONObject(lastJsonLine.toString());

        // 替换message.content字段为合并后的内容
        if (mergedResponse.has("message")) {
            JSONObject message = mergedResponse.getJSONObject("message");
            message.put("content", mergedContent.toString());
            mergedResponse.put("message", message);
        }

        logger.debug("合并后的响应: {}", mergedResponse.toString());
        return mergedResponse;
    }

    /**
     * 处理文本中的图片路径，将图片路径提取到images数组中，并在文本中添加图片标记
     *
     * @param text 包含图片路径的文本
     * @param images 用于存储图片信息的JSONArray
     * @return 处理后的文本
     */
    private String processImagePaths(String text, JSONArray images) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        logger.debug("Processing text for image paths: {}", text);
        logger.debug("Looking for patterns like '文字：img/path' or '文字：/img/path'");

        // 正则表达式匹配"文字时，后面跟的是webapp下img的路径"的模式
        // 例如：查看流程图：img/process 或 具体流程见：/img/img_视频流交换流程
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("([^：]*：)(\\s*)(/img/[^\\s\\.,;，。；]+|img/[^\\s\\.,;，。；]+)");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        StringBuffer sb = new StringBuffer();
        int imageIndex = 0;

        while (matcher.find()) {
            String prefix = matcher.group(1); // 例如"查看流程图："
            String spaces = matcher.group(2); // 空格
            String imagePath = matcher.group(3); // 例如"img/process"或"/img/img_视频流交换流程"

            // 处理路径，确保格式正确
            if (imagePath.startsWith("/")) {
                // 如果路径已经以/开头，去掉开头的/以避免双斜杠
                imagePath = imagePath.substring(1);
            }

            // 添加.jpg后缀
            String fullImagePath = imagePath + ".jpg";

            // 构建完整的图片URL
            String imageUrl = getServletContext().getContextPath() + "/" + fullImagePath;
            logger.debug("Found image path: {}, full URL with jpg suffix: {}", imagePath, imageUrl);

            // 添加到图片数组
            JSONObject imageObj = new JSONObject();
            imageObj.put("url", imageUrl);
            imageObj.put("index", imageIndex);
            images.put(imageObj);

            // 在文本中替换为带有图片索引的标记
            String replacement = prefix + spaces + "[图片" + (imageIndex + 1) + "]";
            matcher.appendReplacement(sb, java.util.regex.Matcher.quoteReplacement(replacement));

            imageIndex++;
        }
        matcher.appendTail(sb);

        logger.debug("Processed text: {}", sb.toString());
        logger.debug("Found {} images", images.length());

        return sb.toString();
    }

    /**
     * 从内容中提取被```sql和```包围的SQL语句
     *
     * @param content 完整的响应内容
     * @return 提取到的SQL语句，如果没有找到则返回null
     */
    private String extractSqlFromMarkdown(String content) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        logger.debug("开始从内容中提取SQL语句");

        // 定义正则表达式模式，匹配```sql和```之间的内容
        // (?s) 开启单行模式，允许 . 匹配换行符
        // ```sql\s*([\s\S]*?)``` 匹配```sql和```之间的内容（非贪婪模式）
        java.util.regex.Pattern sqlPattern = java.util.regex.Pattern.compile("(?s)```sql\\s*([\\s\\S]*?)```");
        java.util.regex.Matcher sqlMatcher = sqlPattern.matcher(content);

        // 如果找到```sql和```之间的内容
        if (sqlMatcher.find()) {
            String sqlContent = sqlMatcher.group(1).trim();
            logger.debug("从内容中提取到SQL代码块: {}", sqlContent);
            return sqlContent;
        }

        // 如果没有找到```sql，尝试匹配普通的```代码块
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(?s)```([\\s\\S]*?)```");
        java.util.regex.Matcher matcher = pattern.matcher(content);

        // 如果找到普通的```代码块
        if (matcher.find()) {
            String extractedContent = matcher.group(1).trim();
            logger.debug("从内容中提取到普通代码块: {}", extractedContent);

            // 检查提取的内容是否是SQL查询
            if (isSqlQuery(extractedContent)) {
                return extractedContent;
            } else {
                logger.debug("提取的代码块不是SQL查询");
            }
        } else {
            logger.debug("在内容中没有找到代码块");
        }

        // 如果没有找到代码块，检查整个内容是否是SQL查询
        if (isSqlQuery(content)) {
            logger.debug("整个内容是SQL查询");
            return content;
        }

        return null;
    }
}
