package com.unimas.ollamaui.client;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * RESTful客户端，用于发送HTTP请求
 */
public class RestClient {
    private static final Logger logger = LogManager.getLogger(RestClient.class);
    
    private final String baseUrl;
    private final Map<String, String> defaultHeaders;
    
    /**
     * 创建一个RESTful客户端
     * 
     * @param baseUrl 基础URL，例如 "http://example.com/api"
     * @param defaultHeaders 默认请求头
     */
    public RestClient(String baseUrl, Map<String, String> defaultHeaders) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.defaultHeaders = defaultHeaders;
    }
    
    /**
     * 发送GET请求
     * 
     * @param endpoint 端点路径，例如 "/users"
     * @return 响应内容
     * @throws IOException 如果请求失败
     */
    public String get(String endpoint) throws IOException {
        String url = buildUrl(endpoint);
        logger.debug("Sending GET request to: {}", url);
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            
            // 添加默认请求头
            addDefaultHeaders(httpGet);
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return handleResponse(response);
            }
        }
    }
    
    /**
     * 发送POST请求，使用JSON请求体
     * 
     * @param endpoint 端点路径，例如 "/users"
     * @param jsonBody JSON请求体
     * @return 响应内容
     * @throws IOException 如果请求失败
     */
    public String post(String endpoint, JSONObject jsonBody) throws IOException {
        String url = buildUrl(endpoint);
        logger.debug("Sending POST request to: {}", url);
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            
            // 添加默认请求头
            addDefaultHeaders(httpPost);
            
            // 设置Content-Type为application/json
            httpPost.setHeader("Content-Type", "application/json");
            
            // 设置请求体
            if (jsonBody != null) {
                StringEntity entity = new StringEntity(jsonBody.toString(), StandardCharsets.UTF_8);
                httpPost.setEntity(entity);
                logger.debug("Request body: {}", jsonBody);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return handleResponse(response);
            }
        }
    }
    
    /**
     * 发送POST请求，使用表单数据
     * 
     * @param endpoint 端点路径，例如 "/users"
     * @param formData 表单数据
     * @return 响应内容
     * @throws IOException 如果请求失败
     */
    public String postForm(String endpoint, Map<String, String> formData) throws IOException {
        String url = buildUrl(endpoint);
        logger.debug("Sending POST form request to: {}", url);
        
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            
            // 添加默认请求头
            addDefaultHeaders(httpPost);
            
            // 设置Content-Type为application/x-www-form-urlencoded
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            
            // 构建表单数据
            if (formData != null && !formData.isEmpty()) {
                StringBuilder formBody = new StringBuilder();
                for (Map.Entry<String, String> entry : formData.entrySet()) {
                    if (formBody.length() > 0) {
                        formBody.append("&");
                    }
                    formBody.append(entry.getKey()).append("=").append(entry.getValue());
                }
                
                StringEntity entity = new StringEntity(formBody.toString(), StandardCharsets.UTF_8);
                httpPost.setEntity(entity);
                logger.debug("Form data: {}", formBody);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return handleResponse(response);
            }
        }
    }
    
    /**
     * 构建完整URL
     * 
     * @param endpoint 端点路径
     * @return 完整URL
     */
    private String buildUrl(String endpoint) {
        if (endpoint == null || endpoint.isEmpty()) {
            return baseUrl;
        }
        
        if (endpoint.startsWith("/")) {
            return baseUrl + endpoint;
        } else {
            return baseUrl + "/" + endpoint;
        }
    }
    
    /**
     * 添加默认请求头
     * 
     * @param request HTTP请求
     */
    private void addDefaultHeaders(org.apache.http.client.methods.HttpRequestBase request) {
        if (defaultHeaders != null) {
            for (Map.Entry<String, String> entry : defaultHeaders.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }
    
    /**
     * 处理HTTP响应
     * 
     * @param response HTTP响应
     * @return 响应内容
     * @throws IOException 如果处理响应失败
     */
    private String handleResponse(CloseableHttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        logger.debug("Response status code: {}", statusCode);
        
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            logger.debug("Response body: {}", responseBody);
            
            if (statusCode >= 200 && statusCode < 300) {
                return responseBody;
            } else {
                logger.error("Request failed with status code: {}", statusCode);
                throw new IOException("Request failed with status code: " + statusCode + ", response: " + responseBody);
            }
        } else {
            logger.warn("Empty response body");
            if (statusCode >= 200 && statusCode < 300) {
                return "";
            } else {
                throw new IOException("Request failed with status code: " + statusCode);
            }
        }
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 创建一个RESTful客户端
        RestClient client = new RestClient("https://jsonplaceholder.typicode.com", null);
        
        try {
            // 发送GET请求
            String response = client.get("/posts/1");
            System.out.println("GET Response: " + response);
            
            // 发送POST请求
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("title", "foo");
            jsonBody.put("body", "bar");
            jsonBody.put("userId", 1);
            
            String postResponse = client.post("/posts", jsonBody);
            System.out.println("POST Response: " + postResponse);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
