package com.unimas.ollamaui.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for executing Python scripts and processing their output.
 */
public class PythonScriptExecutor {
    private static final Logger logger = LoggerFactory.getLogger(PythonScriptExecutor.class);
    private static final String PREDICT_SCRIPT_PATH = "/home/<USER>/transformer/predict.py";
    private static final Pattern INTENT_PATTERN = Pattern.compile("预测意图:\\s*(\\S+)");

    /**
     * Execute the prediction script with the given message and return the predicted intent.
     *
     * @param message The message to analyze
     * @return The predicted intent string, or null if no intent was detected
     */
    public static String predictIntent(String message) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("python", PREDICT_SCRIPT_PATH, message);
            Process process = processBuilder.start();

            // Read the standard output from the script
            BufferedReader stdoutReader = new BufferedReader(
                new InputStreamReader(process.getInputStream()));

            // Read the error output from the script
            BufferedReader stderrReader = new BufferedReader(
                new InputStreamReader(process.getErrorStream()));

            // Read stdout
            String line;
            StringBuilder output = new StringBuilder();
            while ((line = stdoutReader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // Read stderr
            StringBuilder errorOutput = new StringBuilder();
            while ((line = stderrReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // Log any error output
            if (errorOutput.length() > 0) {
                logger.error("Python script error output: {}", errorOutput.toString());
            }

            // Wait for the process to complete
            int exitCode = process.waitFor();

            // Parse the output to find the alarm_message value
            String scriptOutput = output.toString();
            logger.info("Python script output: {}", scriptOutput);

            // Extract the intent value using regex
            Matcher matcher = INTENT_PATTERN.matcher(scriptOutput);

            if (matcher.find()) {
                String intent = matcher.group(1);
                logger.info("Detected intent: {}", intent);

                if ("alarm_message".equals(intent)) {
                    logger.info("ALARM_MESSAGE DETECTED: {}", message);
                    System.out.println("ALARM_MESSAGE DETECTED: " + message);
                }

                // Return the detected intent
                return intent;
            }

            // Log the exit code
            logger.info("Python script exit code: {}", exitCode);

        } catch (IOException | InterruptedException e) {
            logger.error("Error executing Python script: {}", e.getMessage(), e);
        }

        // Return null if no intent was detected or an error occurred
        return null;
    }

    public static void main(String[] args) {
        String intent = predictIntent("1+1等于几");
        System.out.println("Predicted intent: " + intent);
    }
}
