package com.unimas.ollamaui.db;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * SQL查询工具类，用于执行SQL查询并将结果返回给前端
 */
public class SqlQueryUtil {
    private static final Logger logger = LogManager.getLogger(SqlQueryUtil.class);

    /**
     * 执行SQL查询并返回JSON格式的结果
     *
     * @param sql SQL查询语句
     * @return 包含查询结果的JSONObject，格式为：
     *         {
     *           "success": true/false,
     *           "sql": "执行的SQL语句",
     *           "data": [
     *             {"column1": "value1", "column2": "value2", ...},
     *             ...
     *           ],
     *           "columns": ["column1", "column2", ...],
     *           "error": "错误信息（如果有）",
     *           "affectedRows": 数值（适用于非查询操作）
     *         }
     */
    public static JSONObject executeQuery(String sql) {
        logger.info("执行SQL查询: {}", sql);

        JSONObject result = new JSONObject();
        result.put("sql", sql);

        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;

        try {
            // 获取数据库连接
            conn = DatabaseManager.getConnection();
            logger.debug("已连接到数据库");

            // 创建Statement
            stmt = conn.createStatement();

            // 判断SQL类型
            String sqlType = getSqlType(sql);
            logger.debug("SQL类型: {}", sqlType);

            if ("SELECT".equals(sqlType)) {
                // 执行查询
                rs = stmt.executeQuery(sql);

                // 处理结果集
                JSONArray data = convertResultSetToJsonArray(rs);
                JSONArray columns = getColumnNames(rs.getMetaData());

                result.put("success", true);
                result.put("data", data);
                result.put("columns", columns);
                result.put("rowCount", data.length());

                logger.debug("查询执行成功，返回 {} 行", data.length());
            } else {
                // 执行更新操作
                int affectedRows = stmt.executeUpdate(sql);

                result.put("success", true);
                result.put("affectedRows", affectedRows);
                result.put("data", new JSONArray());

                logger.debug("更新执行成功，影响 {} 行", affectedRows);
            }
        } catch (SQLException e) {
            logger.error("数据库错误", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("data", new JSONArray());
        } finally {
            // 关闭资源
            closeResources(rs, stmt, conn);
        }

        return result;
    }

    /**
     * 执行参数化SQL查询并返回JSON格式的结果
     *
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 包含查询结果的JSONObject
     */
    public static JSONObject executeParameterizedQuery(String sql, Object... params) {
        logger.info("执行参数化SQL查询: {}", sql);

        JSONObject result = new JSONObject();
        result.put("sql", sql);

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            // 获取数据库连接
            conn = DatabaseManager.getConnection();

            // 创建PreparedStatement
            pstmt = conn.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            // 判断SQL类型
            String sqlType = getSqlType(sql);

            if ("SELECT".equals(sqlType)) {
                // 执行查询
                rs = pstmt.executeQuery();

                // 处理结果集
                JSONArray data = convertResultSetToJsonArray(rs);
                JSONArray columns = getColumnNames(rs.getMetaData());

                result.put("success", true);
                result.put("data", data);
                result.put("columns", columns);
                result.put("rowCount", data.length());

                logger.debug("参数化查询执行成功，返回 {} 行", data.length());
            } else {
                // 执行更新操作
                int affectedRows = pstmt.executeUpdate();

                result.put("success", true);
                result.put("affectedRows", affectedRows);
                result.put("data", new JSONArray());

                logger.debug("参数化更新执行成功，影响 {} 行", affectedRows);
            }
        } catch (SQLException e) {
            logger.error("数据库错误", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("data", new JSONArray());
        } finally {
            // 关闭资源
            closeResources(rs, pstmt, conn);
        }

        return result;
    }

    /**
     * 判断SQL语句的类型
     *
     * @param sql SQL语句
     * @return SQL类型（SELECT, INSERT, UPDATE, DELETE等）
     */
    private static String getSqlType(String sql) {
        String trimmedSql = sql.trim().toUpperCase();

        if (trimmedSql.startsWith("SELECT")) {
            return "SELECT";
        } else if (trimmedSql.startsWith("INSERT")) {
            return "INSERT";
        } else if (trimmedSql.startsWith("UPDATE")) {
            return "UPDATE";
        } else if (trimmedSql.startsWith("DELETE")) {
            return "DELETE";
        } else if (trimmedSql.startsWith("CREATE")) {
            return "CREATE";
        } else if (trimmedSql.startsWith("ALTER")) {
            return "ALTER";
        } else if (trimmedSql.startsWith("DROP")) {
            return "DROP";
        } else {
            return "OTHER";
        }
    }

    /**
     * 将ResultSet转换为JSONArray
     *
     * @param rs 结果集
     * @return 包含查询结果的JSONArray
     * @throws SQLException 如果转换失败
     */
    private static JSONArray convertResultSetToJsonArray(ResultSet rs) throws SQLException {
        JSONArray jsonArray = new JSONArray();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        // 获取列名
        List<String> columnNames = new ArrayList<>();
        for (int i = 1; i <= columnCount; i++) {
            columnNames.add(metaData.getColumnLabel(i));
        }

        // 遍历结果集
        while (rs.next()) {
            JSONObject row = new JSONObject();

            // 遍历每一列
            for (int i = 1; i <= columnCount; i++) {
                String columnName = columnNames.get(i - 1);
                Object value = rs.getObject(i);

                // 处理null值
                if (value == null) {
                    row.put(columnName, JSONObject.NULL);
                } else {
                    row.put(columnName, value);
                }
            }

            jsonArray.put(row);
        }

        return jsonArray;
    }

    /**
     * 获取结果集的列名
     *
     * @param metaData 结果集元数据
     * @return 包含列名的JSONArray
     * @throws SQLException 如果获取失败
     */
    private static JSONArray getColumnNames(ResultSetMetaData metaData) throws SQLException {
        JSONArray columns = new JSONArray();
        int columnCount = metaData.getColumnCount();

        for (int i = 1; i <= columnCount; i++) {
            columns.put(metaData.getColumnLabel(i));
        }

        return columns;
    }

    /**
     * 关闭数据库资源
     *
     * @param rs 结果集
     * @param stmt 语句
     * @param conn 连接
     */
    private static void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) conn.close();
            logger.debug("数据库资源已关闭");
        } catch (SQLException e) {
            logger.error("关闭数据库资源时出错", e);
        }
    }

    /**
     * 验证SQL语句是否安全（简单检查）
     *
     * @param sql SQL语句
     * @return 如果SQL语句安全则返回true，否则返回false
     */
    public static boolean isSqlSafe(String sql) {
        // 简单的SQL注入检查
        String lowerSql = sql.toLowerCase();

        // 检查是否包含多条语句
        if (lowerSql.contains(";")) {
            logger.warn("SQL语句包含分号，可能尝试执行多条语句: {}", sql);
            return false;
        }

        // 检查是否包含危险关键字
        String[] dangerousKeywords = {
            "drop table", "drop database", "truncate table",
            "delete from", "update", "insert into",
            "alter table", "create table", "grant", "revoke"
        };

        for (String keyword : dangerousKeywords) {
            if (lowerSql.contains(keyword)) {
                logger.warn("SQL语句包含危险关键字 '{}': {}", keyword, sql);
                return false;
            }
        }

        return true;
    }

    /**
     * 测试用的main方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("开始测试SQL查询工具...");

        try {
            // 测试数据库连接
            System.out.println("测试数据库连接...");
            boolean connected = DatabaseManager.testConnection();
            System.out.println("数据库连接测试结果: " + (connected ? "成功" : "失败"));

            if (!connected) {
                System.err.println("无法连接到数据库，测试终止");
                return;
            }

            // 测试简单查询
            String testQuery1 = "SELECT 1 as test";
            System.out.println("\n测试查询 1: " + testQuery1);
            JSONObject result1 = executeQuery(testQuery1);
            System.out.println("查询结果: " + result1.toString(2));

            // 打印数据库中的所有表
            System.out.println("\n数据库中的表:");
            try (Connection conn = DatabaseManager.getConnection();
                 Statement stmt = conn.createStatement()) {

                ResultSet tables = conn.getMetaData().getTables(null, null, "%", new String[]{"TABLE"});
                int tableCount = 0;
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    System.out.println(" - " + tableName);
                    tableCount++;
                }
                tables.close();

                if (tableCount == 0) {
                    System.out.println("数据库中没有表，无法测试表查询");
                    return;
                }
            }

            // 测试特定表查询 - 使用您数据库中的实际表
            String tableName = "sync_task"; // 假设您的数据库中有这个表，如果没有请替换为实际表名

            // 测试表查询
            String testQuery2 = "SELECT * FROM " + tableName + " LIMIT 5";
            System.out.println("\n测试查询 2: " + testQuery2);
            JSONObject result2 = executeQuery(testQuery2);
            System.out.println("查询结果: " + result2.toString(2));

            // 测试计数查询
            String testQuery3 = "SELECT COUNT(*) as count FROM " + tableName;
            System.out.println("\n测试查询 3: " + testQuery3);
            JSONObject result3 = executeQuery(testQuery3);
            System.out.println("查询结果: " + result3.toString(2));

            // 测试错误查询
            String testQuery4 = "SELECT * FROM non_existent_table";
            System.out.println("\n测试查询 4 (错误查询): " + testQuery4);
            JSONObject result4 = executeQuery(testQuery4);
            System.out.println("查询结果: " + result4.toString(2));

            // 测试参数化查询
            System.out.println("\n测试参数化查询");
            String testQuery5 = "SELECT ? as param1, ? as param2";
            System.out.println("测试查询 5: " + testQuery5);
            JSONObject result5 = executeParameterizedQuery(testQuery5, "value1", 42);
            System.out.println("查询结果: " + result5.toString(2));

        } catch (Exception e) {
            System.err.println("测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭连接池
            DatabaseManager.closeDataSource();
        }

        System.out.println("\n测试完成");
    }
}
