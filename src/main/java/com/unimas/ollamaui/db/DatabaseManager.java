package com.unimas.ollamaui.db;

import com.unimas.ollamaui.config.AppConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接池管理类
 */
public class DatabaseManager {
    private static final Logger logger = LogManager.getLogger(DatabaseManager.class);

    private static HikariDataSource dataSource;

    static {
        try {
            initDataSource();
        } catch (Exception e) {
            logger.error("初始化数据库连接池失败", e);
        }
    }

    /**
     * 初始化数据库连接池
     */
    private static void initDataSource() {
        logger.info("初始化数据库连接池...");

        HikariConfig config = new HikariConfig();
        config.setDriverClassName(AppConfig.getDbDriver());

        // 使用原始JDBC URL
        String jdbcUrl = AppConfig.getDbUrl();
        logger.info("JDBC URL: {}", jdbcUrl);

        config.setJdbcUrl(jdbcUrl);
        config.setUsername(AppConfig.getDbUsername());
        config.setPassword(AppConfig.getDbPassword());

        // 添加与 MySQL 5.1 兼容的连接属性
        config.addDataSourceProperty("useUnicode", "true");
        config.addDataSourceProperty("characterEncoding", "GBK");
        config.addDataSourceProperty("zeroDateTimeBehavior", "convertToNull");
        config.addDataSourceProperty("autoReconnect", "true");
        config.addDataSourceProperty("failOverReadOnly", "false");
        config.setMaximumPoolSize(AppConfig.getDbPoolMaxSize());
        config.setMinimumIdle(AppConfig.getDbPoolMinIdle());
        config.setIdleTimeout(AppConfig.getDbPoolIdleTimeout());
        config.setConnectionTimeout(AppConfig.getDbPoolConnectionTimeout());

        // 连接测试查询
        config.setConnectionTestQuery("SELECT 1");

        // 连接池名称
        config.setPoolName("OllamaUIHikariCP");

        // 自动提交
        config.setAutoCommit(true);

        // 连接超时时间
        config.setValidationTimeout(5000);

        // 允许连接在池中的最长生存时间
        config.setMaxLifetime(1800000); // 30分钟

        // 注册JMX监控
        config.setRegisterMbeans(true);

        dataSource = new HikariDataSource(config);

        logger.info("数据库连接池初始化完成");
    }

    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     * @throws SQLException 如果获取连接失败
     */
    public static Connection getConnection() throws SQLException {
        if (dataSource == null) {
            synchronized (DatabaseManager.class) {
                if (dataSource == null) {
                    initDataSource();
                }
            }
        }

        if (dataSource.isClosed()) {
            logger.warn("数据库连接池已关闭，重新初始化");
            synchronized (DatabaseManager.class) {
                if (dataSource.isClosed()) {
                    initDataSource();
                }
            }
        }

        Connection conn = dataSource.getConnection();
        logger.debug("获取数据库连接成功");
        return conn;
    }

    /**
     * 关闭数据库连接池
     */
    public static void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("数据库连接池已关闭");
        }
    }

    /**
     * 测试数据库连接
     *
     * @return 如果连接成功则返回true，否则返回false
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            boolean valid = conn.isValid(5);
            if (valid) {
                logger.info("数据库连接测试成功");
            } else {
                logger.warn("数据库连接测试失败");
            }
            return valid;
        } catch (SQLException e) {
            logger.error("测试数据库连接失败", e);
            return false;
        }
    }




    /**
     * 测试用的main方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("开始测试数据库连接...");

        try {
            // 测试数据库连接
            boolean connected = testConnection();
            System.out.println("数据库连接测试结果: " + (connected ? "成功" : "失败"));

            if (connected) {
                // 测试执行简单查询
                try (Connection conn = getConnection();
                     java.sql.Statement stmt = conn.createStatement();
                     java.sql.ResultSet rs = stmt.executeQuery("SELECT 1 as test")) {

                    if (rs.next()) {
                        System.out.println("测试查询成功，结果: " + rs.getInt("test"));
                    }

                }

                // 测试执行表查询
                try (Connection conn = getConnection();
                     java.sql.Statement stmt = conn.createStatement()) {

                    // 获取所有表
                    java.sql.ResultSet tables = conn.getMetaData().getTables(null, null, "%", new String[]{"TABLE"});
                    System.out.println("数据库中的表:");
                    while (tables.next()) {
                        String tableName = tables.getString("TABLE_NAME");
                        System.out.println(" - " + tableName);

                        // 获取表结构
                        java.sql.ResultSet columns = conn.getMetaData().getColumns(null, null, tableName, null);
                        System.out.println("   列信息:");
                        while (columns.next()) {
                            String columnName = columns.getString("COLUMN_NAME");
                            String columnType = columns.getString("TYPE_NAME");
                            System.out.println("    - " + columnName + " (类型: " + columnType + ")");
                        }
                        columns.close();

                        // 查询表中的数据数量
                        try {
                            java.sql.ResultSet countRs = stmt.executeQuery("SELECT COUNT(*) as count FROM " + tableName);
                            if (countRs.next()) {
                                int count = countRs.getInt("count");
                                System.out.println("   数据行数: " + count);
                            }
                            countRs.close();
                        } catch (SQLException e) {
                            System.out.println("   无法查询表 " + tableName + " 的数据数量: " + e.getMessage());
                        }
                    }
                    tables.close();
                }
            }
        } catch (Exception e) {
            System.err.println("测试过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭连接池
            closeDataSource();
            System.out.println("测试完成，连接池已关闭");
        }
    }
}
