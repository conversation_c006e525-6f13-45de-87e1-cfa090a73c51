package com.unimas.ollamaui;

import java.io.IOException;
import java.util.List;

public class StartUp {
    public static void main(String[] args) {
        try {
            // 创建Ollama客户端
            OllamaClient ollama = new OllamaClient("10.68.41.41", 11434);
//
//            // 生成文本
            String model = "deepseek-r1:7b"; // 指定模型
            String prompt = "为什么天空是蓝色的？"; // 提供提示
//            String response = ollama.generate(model, prompt);
//            // 打印生成的响应
//            System.out.println("生成响应: " + response);

            // 聊天
            String chatResponse = ollama.chat(model, "你好，请介绍一下自己");
            System.out.println("聊天响应: " + chatResponse);

            // 获取模型列表
//            List<OllamaClient.ModelInfo> models = ollama.listModels();
//            System.out.println("\n可用模型列表:");
//            for (OllamaClient.ModelInfo m : models) {
//                System.out.println(m.getName());
//            }

            // 关闭客户端
            ollama.close();
        } catch (IOException e) {
            System.err.println("发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
