package com.unimas.ollamaui;

import com.unimas.ollamaui.config.AppConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.servlet.DefaultServlet;
import org.eclipse.jetty.servlet.FilterHolder;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.eclipse.jetty.util.resource.Resource;

import javax.servlet.DispatcherType;
import java.io.File;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.EnumSet;

/**
 * 嵌入式Jetty服务器，用于启动Web应用
 */
public class JettyServer {
    private static final Logger logger = LogManager.getLogger(JettyServer.class);

    private final Server server;
    private final int port;

    /**
     * 创建一个Jetty服务器
     *
     * @param port 服务器端口
     */
    public JettyServer(int port) {
        this.port = port;
        this.server = new Server(port);
    }

    /**
     * 启动服务器
     *
     * @throws Exception 如果启动失败
     */
    public void start() throws Exception {
        logger.info("Starting Jetty server on port {}...", port);

        // 创建Servlet上下文
        ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
        context.setContextPath("/");
        logger.debug("Created ServletContextHandler with context path: /");

        // 设置资源基础路径
        logger.debug("Setting up resource base path...");
        URL webappUrl = JettyServer.class.getClassLoader().getResource("webapp");
        if (webappUrl == null) {
            logger.debug("Resource 'webapp' not found in classpath, trying filesystem...");
            // 如果在JAR中找不到资源，尝试在文件系统中查找
            String path = JettyServer.class.getProtectionDomain().getCodeSource().getLocation().getPath();
            String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
            File webappDir = new File(new File(decodedPath).getParentFile(), "webapp");
            logger.debug("Checking for webapp directory at: {}", webappDir.getAbsolutePath());

            if (webappDir.exists() && webappDir.isDirectory()) {
                webappUrl = webappDir.toURI().toURL();
                logger.debug("Found webapp directory at: {}", webappDir.getAbsolutePath());
            } else {
                // 尝试查找src/main/webapp目录
                File srcWebappDir = new File("src/main/webapp");
                logger.debug("Checking for webapp directory at: {}", srcWebappDir.getAbsolutePath());

                if (srcWebappDir.exists() && srcWebappDir.isDirectory()) {
                    webappUrl = srcWebappDir.toURI().toURL();
                    logger.debug("Found webapp directory at: {}", srcWebappDir.getAbsolutePath());
                } else {
                    logger.error("Could not find webapp resource directory");
                    throw new RuntimeException("无法找到webapp资源目录");
                }
            }
        } else {
            logger.debug("Found webapp resource in classpath");
        }

        context.setBaseResource(Resource.newResource(webappUrl));
        logger.debug("Set base resource to: {}", webappUrl);

        // 添加API Servlet
        logger.debug("Registering API Servlet at /api/status");
        ServletHolder apiServletHolder = new ServletHolder(new ApiServlet());
        context.addServlet(apiServletHolder, "/api/status");

        // 添加Model Servlet - 处理模型相关API
        logger.debug("Registering Model Servlet at /api/tags");
        ServletHolder modelServletHolder = new ServletHolder(new ModelServlet());
        context.addServlet(modelServletHolder, "/api/tags");

        // 添加SQL Servlet
        logger.debug("Registering SQL Servlet at /api/chat/sql");
        ServletHolder SQLServletHolder = new ServletHolder(new SQLServlet());
        context.addServlet(SQLServletHolder, "/api/chat/sql");

        // 添加FAQ Servlet
        logger.debug("Registering FAQ Servlet at /api/chat/faq");
        ServletHolder FAQServletHolder = new ServletHolder(new FAQServlet());
        context.addServlet(FAQServletHolder, "/api/chat/faq");

        // 添加默认Servlet处理静态资源
        logger.debug("Registering Default Servlet for static resources");
        ServletHolder defaultServletHolder = new ServletHolder("default", DefaultServlet.class);
        defaultServletHolder.setInitParameter("dirAllowed", "true");
        context.addServlet(defaultServletHolder, "/");

        // 添加CORS过滤器
        logger.debug("Registering CORS Filter");
        FilterHolder corsFilterHolder = new FilterHolder(new CorsFilter());
        context.addFilter(corsFilterHolder, "/*", EnumSet.of(DispatcherType.REQUEST));

        // 添加编码过滤器
        logger.debug("Registering Encoding Filter");
        FilterHolder encodingFilterHolder = new FilterHolder(new EncodingFilter());
        context.addFilter(encodingFilterHolder, "/*", EnumSet.of(DispatcherType.REQUEST));

        // 设置Ollama配置
        logger.debug("Setting Ollama configuration parameters");
        context.setInitParameter("ollamaHost", AppConfig.getOllamaHost());
        context.setInitParameter("ollamaPort", String.valueOf(AppConfig.getOllamaPort()));
        context.setInitParameter("ollamaApiKey", AppConfig.getOllamaApiKey());
        logger.debug("Ollama host: {}, port: {}", AppConfig.getOllamaHost(), AppConfig.getOllamaPort());

        // 设置服务器处理器
        logger.debug("Setting server handler");
        server.setHandler(context);

        // 启动服务器
        logger.info("Starting server...");
        server.start();
        logger.info("服务器已启动，访问 http://localhost:" + port);
        System.out.println("服务器已启动，访问 http://localhost:" + port);
    }

    /**
     * 等待服务器关闭
     *
     * @throws InterruptedException 如果等待被中断
     */
    public void join() throws InterruptedException {
        logger.debug("Waiting for server to stop...");
        server.join();
        logger.debug("Server stopped");
    }

    /**
     * 停止服务器
     *
     * @throws Exception 如果停止失败
     */
    public void stop() throws Exception {
        logger.info("Stopping server...");
        server.stop();
        logger.info("Server stopped");
    }

    /**
     * 主方法，用于启动服务器
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 初始化日志
        logger.info("Starting Ollama UI application");
        logger.info("Java version: {}", System.getProperty("java.version"));
        logger.info("OS: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));

        // 解析端口参数
        int port = AppConfig.getServerPort();
        logger.debug("Default port from configuration: {}", port);

        if (args.length > 0) {
            try {
                port = Integer.parseInt(args[0]);
                logger.info("Using port from command line argument: {}", port);
            } catch (NumberFormatException e) {
                logger.warn("Invalid port number: {}, using configured port: {}", args[0], port);
                System.err.println("无效的端口号: " + args[0] + "，使用配置的端口: " + port);
            }
        }

        // 创建并启动服务器
        logger.info("Creating Jetty server instance on port {}", port);
        JettyServer server = new JettyServer(port);
        try {
            server.start();
            server.join();
        } catch (Exception e) {
            logger.error("启动服务器时出错", e);
            System.err.println("启动服务器时出错: " + e.getMessage());
            e.printStackTrace();
        }
        logger.info("Ollama UI application stopped");
    }
}
