package com.unimas.ollamaui;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * Ollama客户端，使用Apache HttpClient实现
 */
public class OllamaClient {
    private final String baseUrl;
    private final String apiKey;
    private final CloseableHttpClient httpClient;

    /**
     * 创建一个Ollama客户端
     *
     * @param host Ollama服务器主机地址
     * @param port Ollama服务器端口
     */
    public OllamaClient(String host, int port) {
        this(host, port, null);
    }

    /**
     * 创建一个Ollama客户端
     *
     * @param host Ollama服务器主机地址
     * @param port Ollama服务器端口
     * @param apiKey API密钥（如果需要）
     */
    public OllamaClient(String host, int port, String apiKey) {
        this.baseUrl = "http://" + host + ":" + port;
        this.apiKey = apiKey;
        this.httpClient = HttpClients.createDefault();
    }

    /**
     * 创建一个Ollama客户端
     *
     * @param baseUrl Ollama服务器基础URL
     * @param apiKey API密钥（如果需要）
     */
    public OllamaClient(String baseUrl, String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.httpClient = HttpClients.createDefault();
    }

    /**
     * 生成文本
     *
     * @param model 模型名称
     * @param prompt 提示文本
     * @return 生成的响应文本
     * @throws IOException 如果请求失败
     */
    public String generate(String model, String prompt) throws IOException {
        String url = baseUrl + "/api/generate";

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", model);
        requestBody.put("prompt", prompt);

        // 打印请求信息
        System.out.println("请求URL: " + url);
        System.out.println("请求JSON: " + requestBody.toString(4));

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        if (apiKey != null && !apiKey.isEmpty()) {
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
        }
        httpPost.setEntity(new StringEntity(requestBody.toString(), StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // 明确指定UTF-8编码
                String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                System.out.println("响应JSON: " + result);

                // 处理响应，只返回内容
                JSONObject jsonResponse = new JSONObject(result);
                return jsonResponse.optString("response", "No response from model");
            } else {
                return "Empty response from server";
            }
        }
    }

    /**
     * 聊天
     *
     * @param model 模型名称
     * @param prompt 提示文本
     * @return 生成的响应文本
     * @throws IOException 如果请求失败
     */
    public String chat(String model, String prompt) throws IOException {
        String url = baseUrl + "/api/chat";

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", model);
        requestBody.put("messages", new JSONArray().put(
            new JSONObject()
                .put("role", "user")
                .put("content", prompt)
        ));
        // 添加stream参数，设置为false，避免流式响应
        requestBody.put("stream", false);

        // 打印请求信息
        System.out.println("请求URL: " + url);
        System.out.println("请求JSON: " + requestBody.toString(4));

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        if (apiKey != null && !apiKey.isEmpty()) {
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
        }
        httpPost.setEntity(new StringEntity(requestBody.toString(), StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // 明确指定UTF-8编码
                String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                System.out.println("响应JSON: " + result);

                // 处理响应
                JSONObject jsonResponse = new JSONObject(result);
                JSONObject messageObj = jsonResponse.optJSONObject("message");
                if (messageObj != null) {
                    // 只返回消息内容
                    return messageObj.optString("content", "No response from model");
                } else {
                    // 如果没有message字段，尝试获取response字段
                    return jsonResponse.optString("response", "No response from model");
                }
            } else {
                // 返回错误信息
                return "Empty response from server";
            }
        }
    }

    /**
     * 获取可用模型列表
     *
     * @return 模型列表
     * @throws IOException 如果请求失败
     */
    public List<ModelInfo> listModels() throws IOException {
        String url = baseUrl + "/api/tags";

        // 打印请求信息
        System.out.println("请求URL: " + url);
        System.out.println("请求方法: GET");

        HttpGet httpGet = new HttpGet(url);
        if (apiKey != null && !apiKey.isEmpty()) {
            httpGet.setHeader("Authorization", "Bearer " + apiKey);
        }

        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // 明确指定UTF-8编码
                String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                System.out.println("响应JSON: " + result);
                JSONObject jsonResponse = new JSONObject(result);
                JSONArray modelsArray = jsonResponse.optJSONArray("models");

                List<ModelInfo> models = new ArrayList<>();
                if (modelsArray != null) {
                    for (int i = 0; i < modelsArray.length(); i++) {
                        JSONObject modelObj = modelsArray.getJSONObject(i);
                        String name = modelObj.optString("name", "");
                        String modified = modelObj.optString("modified_at", "");
                        long size = modelObj.optLong("size", 0);

                        models.add(new ModelInfo(name, modified, size));
                    }
                }

                return models;
            } else {
                return new ArrayList<>();
            }
        }
    }

    /**
     * 关闭客户端
     *
     * @throws IOException 如果关闭失败
     */
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }

    /**
     * 模型信息类
     */
    public static class ModelInfo {
        private final String name;
        private final String modifiedAt;
        private final long size;

        public ModelInfo(String name, String modifiedAt, long size) {
            this.name = name;
            this.modifiedAt = modifiedAt;
            this.size = size;
        }

        public String getName() {
            return name;
        }

        public String getModifiedAt() {
            return modifiedAt;
        }

        public long getSize() {
            return size;
        }

        @Override
        public String toString() {
            return "ModelInfo{" +
                    "name='" + name + '\'' +
                    ", modifiedAt='" + modifiedAt + '\'' +
                    ", size=" + size +
                    '}';
        }
    }
}
