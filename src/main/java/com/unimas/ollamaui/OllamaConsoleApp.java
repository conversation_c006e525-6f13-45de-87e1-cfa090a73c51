package com.unimas.ollamaui;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * Ollama控制台应用程序
 */
public class OllamaConsoleApp {
    private static final String DEFAULT_HOST = "***********";
    private static final int DEFAULT_PORT = 11434;
    private static final String DEFAULT_API_KEY = "8W1986H-8H047C2-HSWQTZY-ZNR2SRQ";

    public static void main(String[] args) {
        String host = DEFAULT_HOST;
        int port = DEFAULT_PORT;
        String apiKey = DEFAULT_API_KEY;

        // 解析命令行参数
        for (int i = 0; i < args.length; i++) {
            if ("-host".equals(args[i]) && i + 1 < args.length) {
                host = args[++i];
            } else if ("-port".equals(args[i]) && i + 1 < args.length) {
                try {
                    port = Integer.parseInt(args[++i]);
                } catch (NumberFormatException e) {
                    System.err.println("无效的端口号: " + args[i]);
                    System.exit(1);
                }
            } else if ("-apiKey".equals(args[i]) && i + 1 < args.length) {
                apiKey = args[++i];
            }
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             Scanner scanner = new Scanner(System.in)) {

            System.out.println("Ollama控制台客户端");
            System.out.println("连接到: " + host + ":" + port);
            System.out.println("输入 'exit' 退出");
            System.out.println("输入 'models' 列出可用模型");
            System.out.println();

            // 默认模型
            String model = "llama2";

            // 列出可用模型
            try {
                List<ModelInfo> models = listModels(httpClient, host, port, apiKey);
                if (!models.isEmpty()) {
                    System.out.println("可用模型:");
                    for (ModelInfo modelInfo : models) {
                        System.out.println("- " + modelInfo.getName());
                    }
                    // 使用第一个模型作为默认模型
                    model = models.get(0).getName();
                    System.out.println("\n当前使用模型: " + model);
                } else {
                    System.out.println("未找到可用模型");
                }
            } catch (IOException e) {
                System.err.println("获取模型列表时出错: " + e.getMessage());
            }

            System.out.println();

            // 主循环
            while (true) {
                System.out.print("> ");
                String input = scanner.nextLine().trim();

                if ("exit".equalsIgnoreCase(input)) {
                    break;
                } else if ("models".equalsIgnoreCase(input)) {
                    try {
                        List<ModelInfo> models = listModels(httpClient, host, port, apiKey);
                        if (!models.isEmpty()) {
                            System.out.println("可用模型:");
                            for (ModelInfo modelInfo : models) {
                                System.out.println("- " + modelInfo.getName());
                            }
                        } else {
                            System.out.println("未找到可用模型");
                        }
                    } catch (IOException e) {
                        System.err.println("获取模型列表时出错: " + e.getMessage());
                    }
                } else if (input.startsWith("model ")) {
                    model = input.substring("model ".length()).trim();
                    System.out.println("已切换到模型: " + model);
                } else if (!input.isEmpty()) {
                    try {
                        String response = chat(httpClient, host, port, apiKey, model, input);
                        System.out.println("\n" + response + "\n");
                    } catch (IOException e) {
                        System.err.println("发送请求时出错: " + e.getMessage());
                    }
                }
            }

            System.out.println("再见!");

        } catch (Exception e) {
            System.err.println("发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 聊天
     */
    private static String chat(CloseableHttpClient httpClient, String host, int port, String apiKey, String model, String prompt) throws IOException {
        String url = "http://" + host + ":" + port + "/api/chat";

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", model);
        requestBody.put("messages", new JSONArray().put(
            new JSONObject()
                .put("role", "user")
                .put("content", prompt)
        ));

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        if (apiKey != null && !apiKey.isEmpty()) {
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
        }
        httpPost.setEntity(new StringEntity(requestBody.toString(), StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                JSONObject jsonResponse = new JSONObject(result);
                JSONObject messageObj = jsonResponse.optJSONObject("message");
                if (messageObj != null) {
                    return messageObj.optString("content", "无响应");
                } else {
                    return jsonResponse.optString("response", "无响应");
                }
            } else {
                return "服务器返回空响应";
            }
        }
    }

    /**
     * 获取可用模型列表
     */
    private static List<ModelInfo> listModels(CloseableHttpClient httpClient, String host, int port, String apiKey) throws IOException {
        String url = "http://" + host + ":" + port + "/api/tags";

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        if (apiKey != null && !apiKey.isEmpty()) {
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
        }

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                JSONObject jsonResponse = new JSONObject(result);
                JSONArray modelsArray = jsonResponse.optJSONArray("models");

                List<ModelInfo> models = new ArrayList<>();
                if (modelsArray != null) {
                    for (int i = 0; i < modelsArray.length(); i++) {
                        JSONObject modelObj = modelsArray.getJSONObject(i);
                        String name = modelObj.optString("name", "");
                        String modified = modelObj.optString("modified_at", "");
                        long size = modelObj.optLong("size", 0);

                        models.add(new ModelInfo(name, modified, size));
                    }
                }

                return models;
            } else {
                return new ArrayList<>();
            }
        }
    }

    /**
     * 模型信息类
     */
    private static class ModelInfo {
        private final String name;
        private final String modifiedAt;
        private final long size;

        public ModelInfo(String name, String modifiedAt, long size) {
            this.name = name;
            this.modifiedAt = modifiedAt;
            this.size = size;
        }

        public String getName() {
            return name;
        }

        public String getModifiedAt() {
            return modifiedAt;
        }

        public long getSize() {
            return size;
        }
    }
}
