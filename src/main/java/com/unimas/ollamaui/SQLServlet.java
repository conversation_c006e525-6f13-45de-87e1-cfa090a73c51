package com.unimas.ollamaui;

import com.unimas.ollamaui.client.SQLPredictionClient;
import com.unimas.ollamaui.config.AppConfig;
import com.unimas.ollamaui.db.SqlQueryUtil;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

/**
 * Ollama代理Servlet，用于转发请求到Ollama服务器
 */
@WebServlet("/sql/*")
public class SQLServlet extends HttpServlet {
    private static final Logger logger = LogManager.getLogger(SQLServlet.class);
    private static final long serialVersionUID = 1L;

    private String ollamaHost;
    private int ollamaPort;
    private String ollamaApiKey;

    @Override
    public void init() throws ServletException {
        super.init();
        logger.info("Initializing OllamaProxyServlet");

        // 从web.xml或环境变量获取配置
        logger.debug("Loading configuration from servlet context");
        ollamaHost = getServletContext().getInitParameter("ollamaHost");
        String ollamaPortStr = getServletContext().getInitParameter("ollamaPort");
        ollamaApiKey = getServletContext().getInitParameter("ollamaApiKey");

        // 如果web.xml中未设置，则使用配置文件中的值
        if (ollamaHost == null || ollamaHost.isEmpty()) {
            logger.debug("ollamaHost not set in web.xml, using value from AppConfig");
            ollamaHost = AppConfig.getOllamaHost();
        }

        if (ollamaPortStr == null || ollamaPortStr.isEmpty()) {
            logger.debug("ollamaPort not set in web.xml, using value from AppConfig");
            ollamaPort = AppConfig.getOllamaPort();
        } else {
            try {
                ollamaPort = Integer.parseInt(ollamaPortStr);
                logger.debug("Using ollamaPort from web.xml: {}", ollamaPort);
            } catch (NumberFormatException e) {
                logger.warn("无效的Ollama端口配置: {}, 使用配置文件中的值: {}", ollamaPortStr, AppConfig.getOllamaPort(), e);
                getServletContext().log("无效的Ollama端口配置，使用配置文件中的值: " + AppConfig.getOllamaPort());
                ollamaPort = AppConfig.getOllamaPort();
            }
        }

        if (ollamaApiKey == null || ollamaApiKey.isEmpty()) {
            logger.debug("ollamaApiKey not set in web.xml, using value from AppConfig");
            ollamaApiKey = AppConfig.getOllamaApiKey();
        } else {
            logger.debug("Using ollamaApiKey from web.xml: [CONFIGURED]");
        }

        logger.info("Ollama代理配置 - 主机: {}, 端口: {}", ollamaHost, ollamaPort);
        getServletContext().log("Ollama代理配置 - 主机: " + ollamaHost + ", 端口: " + ollamaPort);
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 获取请求路径
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "";
        }
        logger.debug("Received GET request with path: {}", pathInfo);

        // 构建Ollama API URL
        String ollamaUrl = "http://" + ollamaHost + ":" + ollamaPort + pathInfo;
        logger.debug("Forwarding to Ollama URL: {}", ollamaUrl);

        // 创建HTTP客户端
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建GET请求
            HttpGet httpGet = new HttpGet(ollamaUrl);
            logger.debug("Created GET request to: {}", ollamaUrl);

            // 添加API密钥（如果有）
            if (ollamaApiKey != null && !ollamaApiKey.isEmpty()) {
                httpGet.setHeader("Authorization", "Bearer " + ollamaApiKey);
                logger.debug("Added Authorization header");
            }

            // 执行请求
            try (CloseableHttpResponse ollamaResponse = httpClient.execute(httpGet)) {
                // 设置响应状态码
                int statusCode = ollamaResponse.getStatusLine().getStatusCode();
                response.setStatus(statusCode);
                logger.debug("Ollama response status code: {}", statusCode);

                // 设置响应头
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                logger.debug("Set response content type to application/json and charset to UTF-8");

                // 获取响应内容
                HttpEntity entity = ollamaResponse.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    logger.debug("Received response from Ollama, length: {} bytes", result.length());

                    // 发送响应
                    PrintWriter out = response.getWriter();
                    out.print(result);
                    out.flush();
                    logger.debug("Response sent to client");
                } else {
                    logger.warn("Received empty response from Ollama");
                }
            }
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 获取请求路径
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "";
        }
        logger.debug("Received POST request with path: {}", pathInfo);

        // 构建Ollama API URL
        String ollamaUrl = "http://" + ollamaHost + ":" + ollamaPort + pathInfo;
        logger.debug("Forwarding to Ollama URL: {}", ollamaUrl);

        // 准备请求JSON
        String requestBodyStr;

        // 如果是聊天API路径，则在后端构建JSON
        if (pathInfo.endsWith("/api/chat") || pathInfo.equals("/api/chat/sql")) {
            // 从请求参数中获取模型和提示词
            String model = request.getParameter("model");
            String prompt = request.getParameter("prompt");
            boolean stream = Boolean.parseBoolean(request.getParameter("stream"));

            if (model == null || model.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Missing model parameter\"}");
                return;
            }

            if (prompt == null || prompt.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Missing prompt parameter\"}");
                return;
            }
            //重新构建prompt
            SQLPredictionClient client = new SQLPredictionClient();
            String table = client.predictIntent(prompt);
//            String table = PythonScriptExecutor.predictIntent(prompt);
            logger.info("Predicted table: {}", table);
            // 根据table,从/etc/unimas/tomcat/conf/prompt/{table}.txt读取prompt
            String promptTemplate = null;
            if (table != null && !table.isEmpty()) {
                promptTemplate = readPromptTemplate(table);
                logger.info("Using prompt template for table: {}", table);
            }

            // 如果找到了模板，使用模板替换原始prompt
            if (promptTemplate != null && !promptTemplate.isEmpty()) {
                // 替换模板中的{prompt}为用户输入
                prompt = promptTemplate.replace("{prompt}", prompt).replace("{model}",model);
                prompt = prompt.replace("{now}", SimpleDateFormat.getDateTimeInstance().format(System.currentTimeMillis()));
                logger.debug("Rebuilt prompt using template: {}", prompt);
            }
            requestBodyStr = prompt;
            // 构建JSON请求
//            JSONObject requestJson = new JSONObject();
//            requestJson.put("model", model);
//
//            JSONArray messages = new JSONArray();
//            JSONObject message = new JSONObject();
//            message.put("role", "user");
//            message.put("content", prompt);
//            messages.put(message);
//
//            requestJson.put("messages", messages);
//            requestJson.put("stream", stream);
//
//            requestBodyStr = requestJson.toString();
            logger.debug("Built JSON request in backend: {}", requestBodyStr);
        } else {
            // 其他API路径，直接读取请求体
            StringBuilder requestBody = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    requestBody.append(line);
                }
            }
            requestBodyStr = requestBody.toString();
            logger.debug("Using client-provided request body, length: {} bytes", requestBodyStr.length());
            if (logger.isTraceEnabled()) {
                logger.trace("Request body: {}", requestBodyStr);
            }
        }

        // 创建HTTP客户端
        logger.debug("Creating HTTP client");
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建POST请求
            HttpPost httpPost = new HttpPost(ollamaUrl);
            httpPost.setHeader("Content-Type", "application/json");
            logger.debug("Created POST request with Content-Type: application/json");

            // 添加API密钥（如果有）
            if (ollamaApiKey != null && !ollamaApiKey.isEmpty()) {
                httpPost.setHeader("Authorization", "Bearer " + ollamaApiKey);
                logger.debug("Added Authorization header");
            }

            // 设置请求体
            httpPost.setEntity(new StringEntity(requestBodyStr, StandardCharsets.UTF_8));
            logger.debug("Set request entity with UTF-8 encoding");

            // 执行请求
            logger.debug("Executing request to Ollama");
            try (CloseableHttpResponse ollamaResponse = httpClient.execute(httpPost)) {
                // 设置响应状态码
                int statusCode = ollamaResponse.getStatusLine().getStatusCode();
                response.setStatus(statusCode);
                logger.debug("Ollama response status code: {}", statusCode);

                // 设置响应头
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                logger.debug("Set response content type to application/json and charset to UTF-8");

                // 获取响应内容
                HttpEntity entity = ollamaResponse.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    logger.debug("Received response from Ollama, length: {} bytes", result.length());
                    if (logger.isTraceEnabled()) {
                        logger.trace("Response body: {}", result);
                    }

                    // 处理流式返回的多行JSON
                    JSONObject mergedResponse = mergeStreamingJsonLines(result);

                    try {
                        // 检查是否包含 message 和 content 字段
                        if (mergedResponse.has("message") && mergedResponse.getJSONObject("message").has("content")) {
                            // 提取content字段中的内容
                            String content = mergedResponse.getJSONObject("message").getString("content").trim();
                            logger.debug("提取到合并后的content内容: {}", content);

                            // 从内容中提取被```sql和```包围的SQL语句
                            String sqlQuery = extractSqlFromMarkdown(content);
                            if (sqlQuery != null && !sqlQuery.isEmpty()) {
                                logger.info("从内容中提取到SQL语句: {}", sqlQuery);
                                content = sqlQuery; // 使用提取出的SQL语句替换原始内容
                            }

                            // 检查content是否是SQL查询语句
                            if (isSqlQuery(content)) {
                                logger.info("检测到SQL查询语句，执行查询: {}", content);

                                // 执行SQL查询并获取结果
                                JSONObject queryResult = SqlQueryUtil.executeQuery(content);

                                // 创建包含原始响应和查询结果的新JSON对象
                                JSONObject combinedResult = new JSONObject();
                                combinedResult.put("original_response", mergedResponse); // 合并后的原始响应
                                combinedResult.put("sql_result", queryResult);         // SQL 查询结果
                                combinedResult.put("sql_query", content);             // SQL 查询语句
                                combinedResult.put("is_sql_query", true);             // 标记这是一个SQL查询响应

                                // 发送组合结果给前端
                                PrintWriter out = response.getWriter();
                                out.print(combinedResult.toString());
                                out.flush();
                                logger.debug("SQL查询结果已发送给客户端");
                                return; // 已处理完毕，返回
                            }
                        }

                        // 如果不是SQL查询或无法解析，直接返回原始结果
                        PrintWriter out = response.getWriter();
                        out.print(result);
                        out.flush();
                        logger.debug("Response sent to client");
                    } catch (Exception e) {
                        // JSON解析错误或其他异常，直接返回原始结果
                        logger.warn("处理响应时出错: {}", e.getMessage());
                        PrintWriter out = response.getWriter();
                        out.print(result);
                        out.flush();
                        logger.debug("Response sent to client (after error)");
                    }
                } else {
                    logger.warn("Received empty response from Ollama");
                }
            }
        }
    }


    /**
     * 从/etc/unimas/tomcat/conf/prompt/{table}.txt读取prompt模板
     *
     * @param table 表名
     * @return prompt模板字符串，如果文件不存在或读取失败则返回null
     */
    private String readPromptTemplate(String table) {
        if (table == null || table.isEmpty()) {
            return null;
        }

        // 构建prompt模板文件路径
        String promptFilePath;
        File promptFile;

        // 首先尝试从标准路径读取
        promptFilePath = "/etc/unimas/tomcat/conf/prompt/" + table + ".txt";
        promptFile = new File(promptFilePath);

        // 如果标准路径不存在，尝试从开发环境路径读取
        if (!promptFile.exists() || !promptFile.isFile()) {
            promptFilePath = "test-prompts/" + table + ".txt";
            promptFile = new File(promptFilePath);
            logger.debug("Standard path not found, trying development path: {}", promptFilePath);
        }

        // 检查文件是否存在
        if (!promptFile.exists() || !promptFile.isFile()) {
            logger.warn("Prompt template file not found: {}", promptFilePath);
            return null;
        }

        try {
            // 读取文件内容
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                    new FileInputStream(promptFile), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }

            // 返回模板内容
            return content.toString().trim();

        } catch (IOException e) {
            logger.error("Error reading prompt template file: {}", promptFilePath, e);
            return null;
        }
    }

    /**
     * 判断字符串是否是SQL查询语句
     *
     * @param text 要检查的文本
     * @return 如果是SQL查询语句则返回true，否则返回false
     */
    private boolean isSqlQuery(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 去除前后空白
        String trimmedText = text.trim();

        // 检查是否以SQL关键字开头
        String lowerText = trimmedText.toLowerCase();

        // 检查是否是SELECT语句
        if (lowerText.startsWith("select")) {
            logger.debug("检测到SELECT语句");
            return true;
        }

        // 检查是否包含常见SQL关键字组合
        String[] sqlPatterns = {
            "from\\s+[\\w_]+\\s+where",
            "select\\s+[\\w\\*,_\\s]+\\s+from",
            "join\\s+[\\w_]+\\s+on",
            "group\\s+by\\s+[\\w_]+",
            "order\\s+by\\s+[\\w_]+"
        };

        for (String pattern : sqlPatterns) {
            if (lowerText.matches(".*" + pattern + ".*")) {
                logger.debug("检测到SQL模式: {}", pattern);
                return true;
            }
        }

        // 如果文本很短且包含SELECT，也认为是SQL
        if (trimmedText.length() < 200 && lowerText.contains("select") && lowerText.contains("from")) {
            logger.debug("检测到简短的SELECT查询");
            return true;
        }

        return false;
    }

    /**
     * 处理流式返回的多行JSON，合并所有行的content字段，并保留最后一行的其他字段
     *
     * @param result 原始响应字符串，包含多行JSON
     * @return 合并后的JSONObject
     */
    private JSONObject mergeStreamingJsonLines(String result) {
        if (result == null || result.isEmpty()) {
            return new JSONObject();
        }

        logger.debug("开始处理流式返回的多行JSON");

        // 按行分割内容
        String[] lines = result.split("\n");
        StringBuilder mergedContent = new StringBuilder();
        JSONObject lastJsonLine = null;

        for (String line : lines) {
            try {
                // 检查是否是JSON格式
                if (line.trim().startsWith("{") && line.trim().endsWith("}")) {
                    JSONObject jsonLine = new JSONObject(line);

                    // 如果包含message和content字段，提取content并添加到合并内容中
                    if (jsonLine.has("message") && jsonLine.getJSONObject("message").has("content")) {
                        String content = jsonLine.getJSONObject("message").getString("content");
                        mergedContent.append(content);
                        logger.debug("从行JSON中提取并添加content: [{}]", content);
                    }

                    // 保存最后一行JSON，用于保留其他字段
                    lastJsonLine = jsonLine;
                }
            } catch (Exception e) {
                logger.debug("解析行为JSON失败: {}", e.getMessage());
            }
        }

        // 如果没有有效的JSON行，返回空对象
        if (lastJsonLine == null) {
            logger.warn("没有找到有效的JSON行");
            return new JSONObject();
        }

        // 创建合并后的响应对象，使用最后一行的其他字段
        JSONObject mergedResponse = new JSONObject(lastJsonLine.toString());

        // 替换message.content字段为合并后的内容
        if (mergedResponse.has("message")) {
            JSONObject message = mergedResponse.getJSONObject("message");
            message.put("content", mergedContent.toString());
            mergedResponse.put("message", message);
        }

        logger.debug("合并后的响应: {}", mergedResponse.toString());
        return mergedResponse;
    }

    /**
     * 从内容中提取被```sql和```包围的SQL语句
     *
     * @param content 完整的响应内容
     * @return 提取到的SQL语句，如果没有找到则返回null
     */
    private String extractSqlFromMarkdown(String content) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        logger.debug("开始从内容中提取SQL语句");

        // 定义正则表达式模式，匹配```sql和```之间的内容
        // (?s) 开启单行模式，允许 . 匹配换行符
        // ```sql\s*([\s\S]*?)``` 匹配```sql和```之间的内容（非贪婪模式）
        java.util.regex.Pattern sqlPattern = java.util.regex.Pattern.compile("(?s)```sql\\s*([\\s\\S]*?)```");
        java.util.regex.Matcher sqlMatcher = sqlPattern.matcher(content);

        // 如果找到```sql和```之间的内容
        if (sqlMatcher.find()) {
            String sqlContent = sqlMatcher.group(1).trim();
            logger.debug("从内容中提取到SQL代码块: {}", sqlContent);
            return sqlContent;
        }

        // 如果没有找到```sql，尝试匹配普通的```代码块
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(?s)```([\\s\\S]*?)```");
        java.util.regex.Matcher matcher = pattern.matcher(content);

        // 如果找到普通的```代码块
        if (matcher.find()) {
            String extractedContent = matcher.group(1).trim();
            logger.debug("从内容中提取到普通代码块: {}", extractedContent);

            // 检查提取的内容是否是SQL查询
            if (isSqlQuery(extractedContent)) {
                return extractedContent;
            } else {
                logger.debug("提取的代码块不是SQL查询");
            }
        } else {
            logger.debug("在内容中没有找到代码块");
        }

        // 如果没有找到代码块，检查整个内容是否是SQL查询
        if (isSqlQuery(content)) {
            logger.debug("整个内容是SQL查询");
            return content;
        }

        return null;
    }
}
