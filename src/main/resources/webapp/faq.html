<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Olla<PERSON> Chat</title>
    <!-- 使用本地Chart.js库 -->
    <script src="js/chart.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .model-selector {
            margin-bottom: 20px;
        }
        select, button, textarea {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3a5ce5;
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .user {
            background-color: #e3f2fd;
            text-align: right;
        }
        .assistant {
            background-color: #f1f1f1;
            text-align: left;
        }
        .system {
            background-color: #fff3cd;
            text-align: center;
        }
        /* Thinking message styles */
        .thinking {
            background-color: #e8eaf6;
            text-align: center;
            font-style: italic;
            color: #555;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .thinking .dots {
            display: inline-block;
            margin-left: 5px;
        }
        .thinking .dots span {
            display: inline-block;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: #555;
            margin: 0 2px;
            animation: dot-pulse 1.5s infinite ease-in-out;
        }
        .thinking .dots span:nth-child(2) {
            animation-delay: 0.2s;
        }
        .thinking .dots span:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes dot-pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.5); opacity: 1; }
        }

        /* SQL result table styles */
        .sql-result {
            margin: 10px 0;
            overflow-x: auto;
        }
        .sql-result table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        .sql-result th {
            background-color: #f2f2f2;
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .sql-result td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .sql-result tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .sql-info {
            margin-bottom: 10px;
            font-style: italic;
        }
        .result-info {
            font-size: 0.9em;
            color: #666;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        textarea {
            flex: 1;
            resize: vertical;
            min-height: 80px;
        }

        /* Chart container styles */
        .chart-container {
            margin: 15px 0;
            max-width: 100%;
            height: 300px;
        }
        .chart-options {
            margin: 10px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .chart-type-button {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
            cursor: pointer;
            font-size: 12px;
        }
        .chart-type-button.active {
            background-color: #4a6cf7;
            color: white;
            border-color: #3a5ce5;
        }

        /* 流式显示光标动画 */
        .cursor {
            animation: blink 1s infinite;
            color: #007bff;
            font-weight: bold;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 流式内容样式 */
        .streaming-content {
            display: inline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ollama Chat</h1>

        <div class="model-selector">
            <select id="modelSelect">
                <option value="" disabled selected>Select a model...</option>
            </select>
            <button id="refreshModels">Refresh Models</button>
        </div>

        <div class="chat-container" id="chatContainer">
            <div class="message system">欢迎使用Ollama Chat! 您可以直接开始提问，无需选择模型（系统会自动使用默认模型）。</div>
        </div>

        <div class="input-area">
            <textarea id="userInput" placeholder="Type your message..."></textarea>
            <button id="sendButton">Send</button>
        </div>
    </div>

    <script>
        // DOM elements
        const modelSelect = document.getElementById('modelSelect');
        const refreshModelsButton = document.getElementById('refreshModels');
        const chatContainer = document.getElementById('chatContainer');
        const userInput = document.getElementById('userInput');
        const sendButton = document.getElementById('sendButton');

        // API endpoints
        const MODELS_ENDPOINT = '/api/tags';
        const CHAT_ENDPOINT = '/faq/api/chat';

        // Thinking message ID
        let thinkingMessageId = null;

        // Chart instances storage - to destroy old charts when creating new ones
        let activeCharts = {};

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadModels();

            refreshModelsButton.addEventListener('click', loadModels);
            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // No special action needed when model selection changes
            modelSelect.addEventListener('change', () => {});
        });

        // Load models
        async function loadModels() {
            try {
                refreshModelsButton.disabled = true;
                refreshModelsButton.textContent = 'Loading...';

                const response = await fetch(MODELS_ENDPOINT, {
                    method: 'GET'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();

                modelSelect.innerHTML = '<option value="" disabled selected>Select a model...</option>';

                if (data.models && data.models.length > 0) {
                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name;
                        option.textContent = model.name;
                        modelSelect.appendChild(option);
                    });

                    // No longer display a message when models are loaded
                } else {
                    addMessage('system', 'No models found. Make sure the Ollama server is running.');
                }
            } catch (error) {
                console.error('Error loading models:', error);
                addMessage('system', `Error loading models: ${error.message}`);
            } finally {
                refreshModelsButton.disabled = false;
                refreshModelsButton.textContent = 'Refresh Models';
            }
        }

        // Add thinking message
        function addThinkingMessage() {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message thinking';
            messageDiv.innerHTML = `
                Thinking
                <div class="dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            return messageDiv;
        }

        // Remove thinking message
        function removeThinkingMessage(messageElement) {
            if (messageElement && messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }

        // Send message
        async function sendMessage() {
            const message = userInput.value.trim();
            let selectedModel = modelSelect.value;

            if (!message) {
                return;
            }

            // Add user message to chat
            addMessage('user', message);

            // Clear the input
            userInput.value = '';

            // 如果没有选择模型，使用默认模型
            if (!selectedModel) {
                // 首先尝试从下拉列表中选择第一个可用的模型
                if (modelSelect.options.length > 1) {
                    // 选择第一个非禁用选项（索引1，因为索引0是占位符）
                    selectedModel = modelSelect.options[1].value;
                    modelSelect.value = selectedModel;
                    console.log(`自动选择模型: ${selectedModel}`);
                } else {
                    // 如果下拉列表中没有可用模型，使用硬编码的默认模型
                    selectedModel = "llama2";  // 设置一个默认模型名称
                    console.log(`使用硬编码的默认模型: ${selectedModel}`);
                }
            }

            // Disable send button and show thinking message
            sendButton.disabled = true;
            const thinkingMessage = addThinkingMessage();

            try {
                // Send request to backend - using URL parameters instead of JSON body
                const url = new URL(CHAT_ENDPOINT, window.location.origin);
                url.searchParams.append('model', selectedModel);
                url.searchParams.append('prompt', message);
                url.searchParams.append('stream', 'true'); // 启用流式响应

                console.log('Sending request to:', url.toString());

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json; charset=UTF-8',
                        'Content-Type': 'application/json; charset=UTF-8'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                // Remove thinking message and create assistant message container
                removeThinkingMessage(thinkingMessage);
                const assistantMessage = addStreamingMessage('assistant');

                // Handle streaming response
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            console.log('Streaming completed');
                            finalizeStreamingMessage(assistantMessage);
                            break;
                        }

                        // Decode the chunk and add to buffer
                        const chunk = decoder.decode(value, { stream: true });
                        buffer += chunk;

                        // Process complete content
                        if (chunk) {
                            // Append the chunk to the message
                            appendToStreamingMessage(assistantMessage, chunk);
                            console.log('Received chunk:', chunk);
                        }
                    }
                } catch (streamError) {
                    console.error('Error reading stream:', streamError);
                    appendToStreamingMessage(assistantMessage, '\n\n[流式传输中断]');
                    finalizeStreamingMessage(assistantMessage);
                } finally {
                    // Ensure the reader is released
                    reader.releaseLock();
                }
            } catch (error) {
                console.error('Error sending message:', error);
                // Remove thinking message
                removeThinkingMessage(thinkingMessage);
                addMessage('system', `Error sending message: ${error.message}`);
            } finally {
                // Always re-enable the send button
                sendButton.disabled = false;
            }
        }

        // Add streaming message container
        function addStreamingMessage(role) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = '<span class="streaming-content"></span><span class="cursor">|</span>';

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            return messageDiv;
        }

        // Append content to streaming message
        function appendToStreamingMessage(messageDiv, content) {
            const contentSpan = messageDiv.querySelector('.streaming-content');
            if (contentSpan) {
                // 将换行符替换为<br>
                const formattedContent = content.replace(/\n/g, '<br>');
                contentSpan.innerHTML += formattedContent;

                // 滚动到底部
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }

        // Finalize streaming message (remove cursor)
        function finalizeStreamingMessage(messageDiv) {
            const cursor = messageDiv.querySelector('.cursor');
            if (cursor) {
                cursor.remove();
            }
        }

        // Add message to chat
        function addMessage(role, content, images) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            // 先将换行符替换为<br>
            let formattedContent = content.replace(/\n/g, '<br>');

            // 然后添加样式
            formattedContent = formattedContent.replace(/问题ID: (.*?)<br>/g, '<strong style="color:#0066cc;">问题ID: $1</strong><br>')
                                             .replace(/问题: (.*?)<br><br>/g, '<strong style="color:#006600;">问题: $1</strong><br><br>');

            // 如果有图片，处理图片显示
            if (images && images.length > 0) {
                // 创建一个临时div来保存HTML内容
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = formattedContent;

                // 遍历所有图片
                for (let i = 0; i < images.length; i++) {
                    const image = images[i];
                    const imgPlaceholder = `[图片${image.index + 1}]`;

                    // 在DOM中查找图片占位符
                    const textNodes = getAllTextNodes(tempDiv);
                    for (let j = 0; j < textNodes.length; j++) {
                        const node = textNodes[j];
                        if (node.nodeValue.includes(imgPlaceholder)) {
                            // 分割文本节点
                            const parts = node.nodeValue.split(imgPlaceholder);

                            // 创建新的文本节点和图片元素
                            const beforeText = document.createTextNode(parts[0]);
                            const imgElement = document.createElement('img');
                            console.log(`Loading image from URL: ${image.url}`);
                            imgElement.src = image.url;
                            imgElement.alt = `图片${image.index + 1}`;
                            imgElement.style.maxWidth = '100%';
                            imgElement.style.marginTop = '10px';
                            imgElement.style.marginBottom = '10px';

                            // 添加图片加载成功处理
                            imgElement.onload = function() {
                                console.log(`Successfully loaded image: ${image.url}`);
                            };

                            // 添加图片加载错误处理
                            imgElement.onerror = function() {
                                console.error(`Failed to load image: ${image.url}`);
                                this.style.display = 'none';
                                const errorText = document.createElement('span');
                                errorText.textContent = `[图片加载失败: ${image.url}]`;
                                errorText.style.color = 'red';
                                errorText.style.fontStyle = 'italic';
                                this.parentNode.insertBefore(errorText, this.nextSibling);
                            };
                            const afterText = document.createTextNode(parts.slice(1).join(imgPlaceholder));

                            // 替换原始节点
                            const parent = node.parentNode;
                            parent.insertBefore(beforeText, node);
                            parent.insertBefore(imgElement, node);
                            parent.insertBefore(afterText, node);
                            parent.removeChild(node);
                            break;
                        }
                    }
                }

                // 使用处理后的HTML
                messageDiv.innerHTML = tempDiv.innerHTML;
            } else {
                // 没有图片，直接使用格式化的内容
                messageDiv.innerHTML = formattedContent;
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            return messageDiv;
        }

        // 获取元素中的所有文本节点
        function getAllTextNodes(element) {
            const textNodes = [];
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            return textNodes;
        }

        // Add HTML message to chat
        function addHtmlMessage(role, htmlContent) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            // Directly set HTML content
            messageDiv.innerHTML = htmlContent;

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Create HTML table from SQL query result
        function createTableFromSqlResult(sqlResult) {
            // If no data, return a message
            if (!sqlResult.data || sqlResult.data.length === 0) {
                return '<p>Query executed successfully, but returned no data.</p>';
            }

            // Create table
            let tableHtml = '<div class="sql-result" style="overflow-x: auto;">';

            // Add SQL query info
            tableHtml += `<p class="sql-info" style="margin-bottom: 10px; font-style: italic;">Query: <code>${sqlResult.sql}</code></p>`;

            // Add table
            tableHtml += '<table style="width: 100%; border-collapse: collapse; margin-bottom: 10px;">';

            // Add table header
            tableHtml += '<thead><tr style="background-color: #f2f2f2;">';
            if (sqlResult.columns && sqlResult.columns.length > 0) {
                // Use columns array as header
                sqlResult.columns.forEach(column => {
                    tableHtml += `<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">${escapeHtml(column)}</th>`;
                });
            } else {
                // Use first row keys as header
                const firstRow = sqlResult.data[0];
                for (const key in firstRow) {
                    if (Object.prototype.hasOwnProperty.call(firstRow, key)) {
                        tableHtml += `<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">${escapeHtml(key)}</th>`;
                    }
                }
            }
            tableHtml += '</tr></thead>';

            // Add table body
            tableHtml += '<tbody>';
            sqlResult.data.forEach((row, rowIndex) => {
                const rowStyle = rowIndex % 2 === 0 ? '' : 'background-color: #f9f9f9;';
                tableHtml += `<tr style="${rowStyle}">`;

                // Use columns array to ensure consistent order
                if (sqlResult.columns && sqlResult.columns.length > 0) {
                    sqlResult.columns.forEach(column => {
                        const value = row[column];
                        tableHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${formatCellValue(value)}</td>`;
                    });
                } else {
                    // If no columns array, use object keys
                    for (const key in row) {
                        if (Object.prototype.hasOwnProperty.call(row, key)) {
                            const value = row[key];
                            tableHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${formatCellValue(value)}</td>`;
                        }
                    }
                }

                tableHtml += '</tr>';
            });
            tableHtml += '</tbody>';
            tableHtml += '</table>';

            // Add result info
            if (sqlResult.rowCount !== undefined) {
                tableHtml += `<p class="result-info" style="font-size: 0.9em; color: #666;">Returned ${sqlResult.rowCount} rows</p>`;
            } else if (sqlResult.affectedRows !== undefined) {
                tableHtml += `<p class="result-info" style="font-size: 0.9em; color: #666;">Affected ${sqlResult.affectedRows} rows</p>`;
            }

            tableHtml += '</div>';

            return tableHtml;
        }

        // Format cell value
        function formatCellValue(value) {
            if (value === null || value === undefined || value === 'null') {
                return '<em style="color: #999;">NULL</em>';
            } else if (typeof value === 'object') {
                return `<code>${escapeHtml(JSON.stringify(value))}</code>`;
            } else {
                return escapeHtml(String(value));
            }
        }

        // Escape HTML special characters
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // 检查数据是否适合可视化
        function isDataVisualizationCompatible(sqlResult) {
            // 需要至少有两列数据，且至少有一行
            if (!sqlResult.data || sqlResult.data.length === 0 || !sqlResult.columns || sqlResult.columns.length < 2) {
                return false;
            }

            // 检查是否有数值类型数据，通常第二列之后的数据适合作为图表的数值
            let hasNumericData = false;
            for (let i = 1; i < sqlResult.columns.length; i++) {
                const column = sqlResult.columns[i];
                for (let row of sqlResult.data) {
                    if (typeof row[column] === 'number') {
                        hasNumericData = true;
                        break;
                    }
                }
                if (hasNumericData) break;
            }

            return hasNumericData;
        }

        // 创建图表选项
        function createChartOptions(sqlResult) {
            const chartId = 'chart-' + Date.now();
            const containerId = 'chart-container-' + Date.now();

            let html = `
                <div class="chart-options">
                    <div>选择图表类型:</div>
                    <button class="chart-type-button active" data-chart-type="bar" data-container-id="${containerId}" data-chart-id="${chartId}">柱状图</button>
                    <button class="chart-type-button" data-chart-type="line" data-container-id="${containerId}" data-chart-id="${chartId}">折线图</button>
                    <button class="chart-type-button" data-chart-type="pie" data-container-id="${containerId}" data-chart-id="${chartId}">饼图</button>
                </div>
                <div class="chart-container" id="${containerId}">
                    <canvas id="${chartId}"></canvas>
                </div>
            `;

            // 在下一个事件循环中初始化图表，确保DOM已经渲染
            setTimeout(() => {
                initializeChart(chartId, 'bar', sqlResult);

                // 为图表类型按钮添加事件监听器
                document.querySelectorAll(`.chart-type-button[data-chart-id="${chartId}"]`).forEach(button => {
                    button.addEventListener('click', (e) => {
                        // 更新活跃状态
                        document.querySelectorAll(`.chart-type-button[data-chart-id="${chartId}"]`).forEach(b => {
                            b.classList.remove('active');
                        });
                        e.target.classList.add('active');

                        // 更新图表类型
                        const chartType = e.target.getAttribute('data-chart-type');
                        initializeChart(chartId, chartType, sqlResult);
                    });
                });
            }, 100);

            return html;
        }

        // 初始化图表
        function initializeChart(chartId, chartType, sqlResult) {
            const ctx = document.getElementById(chartId).getContext('2d');

            // 如果已存在图表，则销毁它
            if (activeCharts[chartId]) {
                activeCharts[chartId].destroy();
            }

            // 确定数据列
            const labelColumn = sqlResult.columns[0]; // 第一列通常作为标签
            const dataColumns = sqlResult.columns.slice(1); // 其他列作为数据

            // 提取数据
            const labels = sqlResult.data.map(row => row[labelColumn]);

            // 根据图表类型创建数据集
            let datasets = [];
            let chartOptions = {};

            if (chartType === 'pie') {
                // 饼图只能显示一个数据系列，默认使用第二列
                const dataColumn = dataColumns[0];
                const backgroundColor = generateRandomColors(labels.length);

                datasets = [{
                    data: sqlResult.data.map(row => row[dataColumn]),
                    backgroundColor: backgroundColor,
                    borderWidth: 1
                }];

                chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        }
                    }
                };
            } else {
                // 柱状图和折线图可以显示多个数据系列
                datasets = dataColumns.map((column, index) => {
                    const color = getColor(index);
                    return {
                        label: column,
                        data: sqlResult.data.map(row => row[column]),
                        backgroundColor: chartType === 'line' ? color : color + '80', // 半透明背景色用于柱状图
                        borderColor: color,
                        borderWidth: 1,
                        tension: 0.1
                    };
                });

                chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                };
            }

            // 创建图表
            activeCharts[chartId] = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: chartOptions
            });
        }

        // 生成随机颜色
        function generateRandomColors(count) {
            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(getColor(i));
            }
            return colors;
        }

        // 获取预定义颜色
        function getColor(index) {
            const colors = [
                '#4a6cf7', '#f94144', '#f3722c', '#f8961e', '#f9c74f',
                '#90be6d', '#43aa8b', '#4d908e', '#577590', '#277da1'
            ];
            return colors[index % colors.length];
        }
    </script>
</body>
</html>
